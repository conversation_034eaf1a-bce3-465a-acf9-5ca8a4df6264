package biz

import (
	"context"
	"errors"
	"time"
)

var (
	ErrAdsenseSiteAdFormatNotFound = errors.New("adsense site ad format not found")
)

type AdsenseSiteAdFormatData struct {
	Date                  time.Time `json:"date"`
	Site                  string    `json:"site"`
	AdFormat              string    `json:"ad_format"`
	EstimatedEarnings     float64   `json:"estimated_earnings"`
	PageViews             int       `json:"page_views"`
	PageViewsRpm          float64   `json:"page_views_rpm"`
	IMPRESSIONS           int       `json:"impressions"`
	ImpressionsRpm        float64   `json:"impressions_rpm"`
	AdRequestsCoverage    float64   `json:"ad_requests_coverage"`
	CLICKS                int       `json:"clicks"`
	AdRequests            int       `json:"ad_requests"`
	ImpressionsCtr        float64   `json:"impressions_ctr"`
	ActiveViewViewability float64   `json:"active_view_viewability"`
	CostPerClick          float64   `json:"cost_per_click"`
	MatchedAdRequests     int       `json:"matched_ad_requests"`
	CooperationChannel    string    `json:"cooperation_channel"`
	ActiveViewTime        int       `json:"active_view_time"`
	Account               string    `json:"account"`
	Platform              string    `json:"platform"`
}

// SiteEarningsResult 站点收益汇总结果
type SiteEarningsResult struct {
	Site          string  `json:"site"`
	TotalEarnings float64 `json:"total_earnings"`
}

type AdSenseSiteAdFormatRepo interface {
	Create(ctx context.Context, data []*AdsenseSiteAdFormatData, hasSaveHistory bool) error
	CreateHKD(ctx context.Context, datas []*AdsenseSiteAdFormatData) error
	CreateHistory(ctx context.Context, datas []*AdsenseSiteAdFormatData) error
	GetSiteEstimatedEarnings(ctx context.Context, start time.Time, end time.Time) ([]*AdsenseSiteAdFormatData, error)
	// GetMaxSiteEstimatedEarnings 获取指定时间范围内每个站点每天的最大收益汇总
	GetMaxSiteEstimatedEarnings(ctx context.Context, start time.Time, end time.Time) ([]*SiteEarningsResult, error)
	// GetSiteReduction 获取站点数据变化比较结果，比较最新两次采集数据的差异
	GetSiteReduction(ctx context.Context, start time.Time, end time.Time) ([]*SiteReductionResult, error)
	ListBusinessSiteData(ctx context.Context, start, end time.Time) ([]*BusinessSiteData, error)
}
