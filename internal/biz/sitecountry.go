package biz

import (
	"context"
	"errors"
	"time"
)

var (
	ErrAdsenseSiteCountryNotFound = errors.New("adsense site country not found")
)

type AdsenseSiteCountryData struct {
	Date                  time.Time `json:"date"`
	Site                  string    `json:"site"`
	Country               string    `json:"country"`
	EstimatedEarnings     float64   `json:"estimated_earnings"`
	PageViews             int       `json:"page_views"`
	PageViewsRpm          float64   `json:"page_views_rpm"`
	IMPRESSIONS           int       `json:"impressions"`
	ImpressionsRpm        float64   `json:"impressions_rpm"`
	AdRequestsCoverage    float64   `json:"ad_requests_coverage"`
	CLICKS                int       `json:"clicks"`
	AdRequests            int       `json:"ad_requests"`
	ImpressionsCtr        float64   `json:"impressions_ctr"`
	ActiveViewViewability float64   `json:"active_view_viewability"`
	CostPerClick          float64   `json:"cost_per_click"`
	MatchedAdRequests     int       `json:"matched_ad_requests"`
	Account               string    `json:"account"`
	Platform              string    `json:"platform"`
}

type AdSenseSiteCountryRepo interface {
	Create(ctx context.Context, data []*AdsenseSiteCountryData) error
	CreateHKDData(ctx context.Context, data []*AdsenseSiteCountryData) error
}
