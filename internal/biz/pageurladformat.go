package biz

import (
	"context"
	"errors"
	"time"
)

var (
	ErrPageURLAdFormatNotFound = errors.New("adsense page url ad format data not found")
)

type AdsensePageURLAdFormatData struct {
	Date                  time.Time `json:"date"`
	PageURL               string    `json:"page_url"`
	AdFormat              string    `json:"ad_format"`
	EstimatedEarnings     float64   `json:"estimated_earnings"`
	PageViews             int       `json:"page_views"`
	PageViewsRpm          float64   `json:"page_views_rpm"`
	IMPRESSIONS           int       `json:"impressions"`
	ImpressionsRpm        float64   `json:"impressions_rpm"`
	AdRequestsCoverage    float64   `json:"ad_requests_coverage"`
	CLICKS                int       `json:"clicks"`
	AdRequests            int       `json:"ad_requests"`
	ImpressionsCtr        float64   `json:"impressions_ctr"`
	ActiveViewViewability float64   `json:"active_view_viewability"`
	CostPerClick          float64   `json:"cost_per_click"`
	MatchedAdRequests     int       `json:"matched_ad_requests"`
	Account               string    `json:"account"`
	Platform              string    `json:"platform"`
}

type AdSensePageURLAdFormatRepo interface {
	Create(ctx context.Context, datas []*AdsensePageURLAdFormatData) error
	CreateHKD(ctx context.Context, datas []*AdsensePageURLAdFormatData) error
}
