package biz

import (
	"context"
	"errors"
	"time"
)

var (
	ErrAdsenseChannelAdFormatNotFound = errors.New("adsense customize channel ad format data not found")
)

type AdsenseChannelAdFormatData struct {
	Date                  time.Time `json:"date"`
	Channel               string    `json:"channel"`
	ChannelID             string    `json:"channel_id"`
	AdFormat              string    `json:"ad_format"`
	EstimatedEarnings     float64   `json:"estimated_earnings"`
	PageViews             int       `json:"page_views"`
	PageViewsRpm          float64   `json:"page_views_rpm"`
	IMPRESSIONS           int       `json:"impressions"`
	ImpressionsRpm        float64   `json:"impressions_rpm"`
	AdRequestsCoverage    float64   `json:"ad_requests_coverage"`
	CLICKS                int       `json:"clicks"`
	AdRequests            int       `json:"ad_requests"`
	ImpressionsCtr        float64   `json:"impressions_ctr"`
	ActiveViewViewability float64   `json:"active_view_viewability"`
	CostPerClick          float64   `json:"cost_per_click"`
	MatchedAdRequests     int       `json:"matched_ad_requests"`
	Account               string    `json:"account"`
	Platform              string    `json:"platform"`
}

type AdSenseChannelAdFormatRepo interface {
	Create(ctx context.Context, datas []*AdsenseChannelAdFormatData) error
	List(ctx context.Context, s, e time.Time) ([]*AdsenseChannelAdFormatData, error)
	CreateHKD(ctx context.Context, datas []*AdsenseChannelAdFormatData) error
	ListHKD(ctx context.Context, date time.Time, date2 time.Time) ([]*AdsenseChannelAdFormatData, error)
}
