package biz

import (
	"context"
	"errors"
	"time"
)

var (
	ErrAdsenseChannelNotFound = errors.New("adsense customize channel  not found")
)

type AdsenseChannelData struct {
	Date                  time.Time `json:"date"`
	Channel               string    `json:"channel"`
	ChannelID             string    `json:"channel_id"`
	EstimatedEarnings     float64   `json:"estimated_earnings"`
	PageViews             int       `json:"page_views"`
	PageViewsRpm          float64   `json:"page_views_rpm"`
	IMPRESSIONS           int       `json:"impressions"`
	ImpressionsRpm        float64   `json:"impressions_rpm"`
	AdRequestsCoverage    float64   `json:"ad_requests_coverage"`
	CLICKS                int       `json:"clicks"`
	AdRequests            int       `json:"ad_requests"`
	ImpressionsCtr        float64   `json:"impressions_ctr"`
	ActiveViewViewability float64   `json:"active_view_viewability"`
	CostPerClick          float64   `json:"cost_per_click"`
	MatchedAdRequests     int       `json:"matched_ad_requests"`
	Account               string    `json:"account"`
	Platform              string    `json:"platform"`
}

type AdSenseChannelRepo interface {
	Create(ctx context.Context, data []*AdsenseChannelData) error
	List(ctx context.Context, start, end time.Time) ([]*AdsenseChannelData, error)
	CreateHKD(ctx context.Context, datas []*AdsenseChannelData) error
	ListHKD(ctx context.Context, date time.Time, date2 time.Time) ([]*AdsenseChannelData, error)
}
