package biz

import (
	"context"
	"time"
)

type JobLog struct {
	Date         time.Time `json:"date"`
	IsSuccess    bool      `json:"is_success"`
	Message      string    `json:"message"`
	Name         string    `json:"name"`
	CurrencyCode string    `json:"currency_code"`
	DateRange    string    `json:"date_range"`
	Account      string    `json:"account"`
	Platform     string    `json:"platform"`
}

type JobLogRepo interface {
	Create(ctx context.Context, jobLog *JobLog) error
}
