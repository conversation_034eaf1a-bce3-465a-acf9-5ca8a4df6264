package biz

import (
	"bytes"
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"math"
	"mime"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/emersion/go-imap/v2"
	"github.com/emersion/go-imap/v2/imapclient"
	"github.com/emersion/go-message/charset"
	"github.com/emersion/go-message/mail"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/xuri/excelize/v2"
	"k8s.io/kubectl/pkg/util/i18n"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/bot"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/cast"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/date"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/email"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/slice"
)

const (
	// 货币代码
	USD = "USD"
	HKD = "HKD"

	// 时区
	TimeZoneShanghai = "Asia/Shanghai"

	// 错误消息
	ErrMsgDateParsing = "Error while parsing date:"

	// Excel相关
	ExcelSheetReport  = "Report"
	ExcelSheetDefault = "Sheet1"
	ExcelSheetReport2 = "Report2"

	// 邮件相关
	EmailInbox           = "INBOX"
	EmailSubjectTechData = "技术数据中台使用"

	// CSV相关
	CSVTotalLabel = "合计"

	// 状态
	StatusEnabled = 1
	StatusUnknown = "未知"

	// 时间格式
	DateFormatYMD = "06-01-2"

	// 浏览器名称
	BrowserAndroidWebview = "Android Webview"
	BrowserSafari         = "Safari"
	BrowserChrome         = "Chrome"
	BrowserEdge           = "Edge"
	BrowserSafariInApp    = "Safari (in-app)"
	BrowserOther          = "other"

	// 字段映射
	FieldName        = "name"
	FieldCooperation = "cooperation"

	// 平台名称
	PlatformAdsense = "Adsense"
)

type Adsense struct {
	Averages struct {
		Cells []struct {
			Value string `json:"value,omitempty"`
		} `json:"cells"`
	} `json:"averages"`
	EndDate struct {
		Day   int `json:"day"`
		Month int `json:"month"`
		Year  int `json:"year"`
	} `json:"endDate"`
	Headers []struct {
		Name         string `json:"name"`
		Type         string `json:"type"`
		CurrencyCode string `json:"currencyCode,omitempty"`
	} `json:"headers"`
	//Rows []struct {
	//	Cells []struct {
	//		Value string `json:"value"`
	//	} `json:"cells"`
	//} `json:"rows"`
	StartDate struct {
		Day   int `json:"day"`
		Month int `json:"month"`
		Year  int `json:"year"`
	} `json:"startDate"`
	TotalMatchedRows string `json:"totalMatchedRows"`
	Totals           struct {
		Cells []struct {
			Value string `json:"value,omitempty"`
		} `json:"cells"`
	} `json:"totals"`
	Data [][]string `json:"data"`
}

type AdsenseRepo interface {
	Generate(ctx context.Context, pubID string, metrics, orderBy, dimensions, filters []string, dateRange, currencyCode string) (*Adsense, error)
	GenerateCsv(ctx context.Context, pubID string, metrics []string, by []string, dimensions []string, filters []string, dateRange string, code string) (*Adsense, error)
	GenerateCsvByCustomDateRange(ctx context.Context, pubID string, metrics []string, by []string, dimensions []string, filters []string, currencyCode string, dateRange *date.Range) (*Adsense, error)
	ListCustomChannels(ctx context.Context, pubID string) ([]*AdsenseCustomChannel, error)
	ListUrlChannels(ctx context.Context, pubID string) ([]*AdsenseUrlChannel, error)
	GetAllPubIDs() []string
	GetPubIDsForJob(jobConfig *conf.Job_JobConfig) ([]string, error)
}
type AdsenseData struct {
	Date                   time.Time `json:"date"`
	AdFormat               string    `json:"ad_format"`
	Country                string    `json:"country"`
	Channel                string    `json:"channel"`
	CP                     string    `json:"cp"`
	PageType               string    `json:"page_type"`
	From                   string    `json:"from"`
	UrlChannel             string    `json:"url_channel"`
	PageURL                string    `json:"page_url"`
	Name                   string    `json:"cooperation_channel"`
	Site                   string    `json:"site"`
	SubChannel             string    `json:"sub_channel"`
	AppId                  string    `json:"app_id"`
	EstimatedEarnings      float64   `json:"estimated_earnings"`
	PageViews              int       `json:"page_views"`
	PageViewsRpm           float64   `json:"page_views_rpm"`
	IMPRESSIONS            int       `json:"impressions"`
	ImpressionsRpm         float64   `json:"impressions_rpm"`
	AdRequestsCoverage     float64   `json:"ad_requests_coverage"`
	CLICKS                 int       `json:"clicks"`
	AdRequests             int       `json:"ad_requests"`
	ImpressionsCtr         float64   `json:"impressions_ctr"`
	ActiveViewViewability  float64   `json:"active_view_viewability"`
	CostPerClick           float64   `json:"cost_per_click"`
	MatchedAdRequests      int       `json:"matched_ad_requests"`
	ImpressionsPerPageView float64   `json:"impressions_per_page_view"`
	Count                  int       `json:"count,omitempty"`
}

// AdsenseUsecase is a bot usecase.
type AdsenseUsecase struct {
	repo                       AdsenseRepo
	adSenseSiteCountryRepo     AdSenseSiteCountryRepo
	pageURLRepo                AdSensePageURLRepo
	channelCountryRepo         AdSenseChannelCountryRepo
	urlChannelCountryRepo      AdSenseURLChannelCountryRepo
	channelRepo                AdSenseChannelRepo
	gameRepo                   AdSenseGameRepo
	gameCountryRepo            AdSenseGameCountryRepo
	channelAdFormatRepo        AdSenseChannelAdFormatRepo
	pageUrlAdFormat            AdSensePageURLAdFormatRepo
	pageURLCountryAdFormatRepo AdSensePageURLCountryAdFormatRepo
	pageURLCountryRepo         AdSensePageURLCountryRepo
	siteRepo                   AdSenseSiteRepo
	tsRepo                     AdSenseSiteTodayRepo
	tscRepo                    AdSenseSiteCountryTodayRepo
	siteAdFormatRepo           AdSenseSiteAdFormatRepo
	siteAdUnitRepo             AdSenseSiteAdUnitRepo
	businessSiteRepo           BusinessSiteRepo
	siteCountryAdFormatRepo    AdSenseSiteCountryAdFormatRepo
	urlChannelRepo             AdSenseURLChannelRepo
	urlChannelAdFormatRepo     AdSenseURLChannelAdFormatRepo
	channelCountryAdFormatRepo AdSenseChannelCountryAdFormatRepo
	urlChannelCountryAdFormat  AdSenseURLChannelCountryAdFormatRepo
	customChannelRepo          AdsenseCustomChannelRepo
	csucRepo                   CooperationSiteUrlChannelRepo
	cRepo                      CooperationRepo
	appCountrySummaryRepo      AppCountrySummaryRepo
	email                      *email.NotifyEmail
	bot                        *bot.Client
	nvTo                       []string
	nvCC                       []string
	djsSite                    []string
	djsTo                      []string
	channelGaReportTo          []string
	log                        *log.Helper
	imapConfig                 *conf.Imap
	adManagerSiteRepo          AdManagerSiteRepo
	unifiedSiteRepo            UnifiedSiteRepo
	tgAdRepo                   TgAdRepo
	countryRepo                CountryRepo
	analyticsSiteRepo          AnalyticsSiteRepo
	adReductionRepo            AdReductionRepo
	minEstimatedEarnings       float64
}

// NewAdsenseUsecase new an adsense usecase.
func NewAdsenseUsecase(e *conf.Email, imapConfig *conf.Imap, djsConf *conf.Djs, channelGaRepoConf *conf.ChannelGaReport, repo AdsenseRepo, adSenseSiteCountryRepo AdSenseSiteCountryRepo, channelCountryRepo AdSenseChannelCountryRepo, gameCountryRepo AdSenseGameCountryRepo,
	pageURLRepo AdSensePageURLRepo, channelRepo AdSenseChannelRepo, channelAdFormatRepo AdSenseChannelAdFormatRepo, adSenseSiteTodayRepo AdSenseSiteTodayRepo, cooperationRepo CooperationRepo,
	adSenseSiteCountryTodayRepo AdSenseSiteCountryTodayRepo, siteCountryAdFormatRepo AdSenseSiteCountryAdFormatRepo, channelCountryAdFormatRepo AdSenseChannelCountryAdFormatRepo,
	urlChannelCountryAdFormat AdSenseURLChannelCountryAdFormatRepo, email *email.NotifyEmail, gameRepo AdSenseGameRepo, cooperationSiteUrlChannelRepo CooperationSiteUrlChannelRepo,
	pageUrlAdFormat AdSensePageURLAdFormatRepo, siteRepo AdSenseSiteRepo, siteAdFormatRepo AdSenseSiteAdFormatRepo, customChannelRepo AdsenseCustomChannelRepo, siteAdUnitRepo AdSenseSiteAdUnitRepo,
	urlChannelRepo AdSenseURLChannelRepo, urlChannelAdFormatRepo AdSenseURLChannelAdFormatRepo, bot *bot.Client, appCountrySummaryRepo AppCountrySummaryRepo, unifiedSiteRepo UnifiedSiteRepo, businessSiteRepo BusinessSiteRepo, pageURLCountryAdFormatRepo AdSensePageURLCountryAdFormatRepo,
	pageURLCountryRepo AdSensePageURLCountryRepo,
	urlChannelCountryRepo AdSenseURLChannelCountryRepo, adManagerSiteRepo AdManagerSiteRepo, tgAdRepo TgAdRepo, countryRepo CountryRepo, analyticsSiteRepo AnalyticsSiteRepo, adReductionRepo AdReductionRepo, logger log.Logger) *AdsenseUsecase {
	return &AdsenseUsecase{
		repo:                       repo,
		adSenseSiteCountryRepo:     adSenseSiteCountryRepo,
		pageURLRepo:                pageURLRepo,
		gameRepo:                   gameRepo,
		gameCountryRepo:            gameCountryRepo,
		csucRepo:                   cooperationSiteUrlChannelRepo,
		cRepo:                      cooperationRepo,
		channelCountryRepo:         channelCountryRepo,
		email:                      email,
		siteAdUnitRepo:             siteAdUnitRepo,
		urlChannelCountryRepo:      urlChannelCountryRepo,
		channelRepo:                channelRepo,
		customChannelRepo:          customChannelRepo,
		channelAdFormatRepo:        channelAdFormatRepo,
		nvTo:                       e.GetNvTo(),
		nvCC:                       e.GetNvCc(),
		channelGaReportTo:          channelGaRepoConf.GetTo(),
		pageUrlAdFormat:            pageUrlAdFormat,
		siteRepo:                   siteRepo,
		channelCountryAdFormatRepo: channelCountryAdFormatRepo,
		urlChannelCountryAdFormat:  urlChannelCountryAdFormat,
		siteAdFormatRepo:           siteAdFormatRepo,
		siteCountryAdFormatRepo:    siteCountryAdFormatRepo,
		urlChannelRepo:             urlChannelRepo,
		tscRepo:                    adSenseSiteCountryTodayRepo,
		tsRepo:                     adSenseSiteTodayRepo,
		urlChannelAdFormatRepo:     urlChannelAdFormatRepo,
		appCountrySummaryRepo:      appCountrySummaryRepo,
		bot:                        bot,
		log:                        log.NewHelper(logger),
		djsSite:                    djsConf.GetSite(),
		djsTo:                      djsConf.GetTo(),
		imapConfig:                 imapConfig,
		adManagerSiteRepo:          adManagerSiteRepo,
		unifiedSiteRepo:            unifiedSiteRepo,
		tgAdRepo:                   tgAdRepo,
		countryRepo:                countryRepo,
		analyticsSiteRepo:          analyticsSiteRepo,
		minEstimatedEarnings:       channelGaRepoConf.GetMinEstimatedEarnings(),
		adReductionRepo:            adReductionRepo,
		businessSiteRepo:           businessSiteRepo,
		pageURLCountryAdFormatRepo: pageURLCountryAdFormatRepo,
		pageURLCountryRepo:         pageURLCountryRepo,
	}
}

func (uc *AdsenseUsecase) FilterCoverage(ctx context.Context, pubID string, metrics, orderBy []string, dateRange, currencyCode, dimensions string) (*Adsense, error) {
	data, err := uc.repo.Generate(ctx, pubID, metrics, orderBy, []string{dimensions}, []string{}, dateRange, currencyCode)
	if err != nil {
		return nil, err
	}
	rows := make([][]string, 0)
	for _, row := range data.Data {
		coverage, err := strconv.ParseFloat(row[6], 64)
		if err != nil {
			return nil, err
		}
		pv, err := strconv.ParseInt(row[2], 10, 64)
		if err != nil {
			return nil, err
		}
		// 筛选pv大于99 广告覆盖率小于0.8的数据
		if coverage <= 0.80 && pv >= 100 {
			rows = append(rows, row)
		}
	}
	data.Data = rows
	return data, nil
}

func (uc *AdsenseUsecase) GetAdSenseDataByCsv(ctx context.Context, pubID string, metrics, orderBy, dimensions, filters []string, dateRange, currencyCode string) (*Adsense, error) {
	data, err := uc.repo.GenerateCsv(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (uc *AdsenseUsecase) GetAdSenseDataByCustomDateRange(ctx context.Context, pubID string, metrics, orderBy, dimensions, filters []string, currencyCode string, dateRange *date.Range) (*Adsense, error) {
	data, err := uc.repo.GenerateCsvByCustomDateRange(ctx, pubID, metrics, orderBy, dimensions, filters, currencyCode, dateRange)
	if err != nil {
		return nil, err
	}
	return data, nil
}
func (uc *AdsenseUsecase) GetAdsenseCustomChannelData(ctx context.Context, pubID string) ([]*AdsenseCustomChannel, error) {
	channels, err := uc.repo.ListCustomChannels(ctx, pubID)
	if err != nil {
		return nil, err
	}
	return channels, nil
}

func (uc *AdsenseUsecase) GetAdSenseData(ctx context.Context, pubID string, metrics, orderBy, dimensions, filters []string, dateRange, currencyCode string) (*Adsense, error) {
	data, err := uc.repo.Generate(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (uc *AdsenseUsecase) SaveSiteCountryData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseSiteCountryData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseSiteCountryData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Site:                  row[0],
			Country:               row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}

		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.adSenseSiteCountryRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.adSenseSiteCountryRepo.CreateHKDData(ctx, datas)
		if err != nil {
			return err
		}
	}
	return nil
}
func (uc *AdsenseUsecase) SaveTodaySiteCountryData(ctx context.Context, data *Adsense) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	for _, row := range data.Data {
		ascd := &AdsenseSiteCountryTodayData{
			Date:                  time.Now().In(cstSh),
			Site:                  row[0],
			Country:               row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
		}
		err := uc.tscRepo.Create(ctx, ascd)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SavePageURLData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsensePageURLData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsensePageURLData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			PageURL:               row[0],
			EstimatedEarnings:     cast.StringToFloat(row[2]),
			PageViews:             cast.StringToInt(row[3]),
			PageViewsRpm:          cast.StringToFloat(row[4]),
			IMPRESSIONS:           cast.StringToInt(row[5]),
			ImpressionsRpm:        cast.StringToFloat(row[6]),
			AdRequestsCoverage:    cast.StringToFloat(row[7]),
			CLICKS:                cast.StringToInt(row[8]),
			AdRequests:            cast.StringToInt(row[9]),
			ImpressionsCtr:        cast.StringToFloat(row[10]),
			ActiveViewViewability: cast.StringToFloat(row[11]),
			CostPerClick:          cast.StringToFloat(row[12]),
			MatchedAdRequests:     cast.StringToInt(row[13]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)
	}
	if currencyCode == USD {
		err := uc.pageURLRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.pageURLRepo.CreateHKDData(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SaveChannelCountryData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseChannelCountryData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[2])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseChannelCountryData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Channel:               row[0],
			ChannelID:             row[1],
			Country:               row[3],
			EstimatedEarnings:     cast.StringToFloat(row[4]),
			PageViews:             cast.StringToInt(row[5]),
			PageViewsRpm:          cast.StringToFloat(row[6]),
			IMPRESSIONS:           cast.StringToInt(row[7]),
			ImpressionsRpm:        cast.StringToFloat(row[8]),
			AdRequestsCoverage:    cast.StringToFloat(row[9]),
			CLICKS:                cast.StringToInt(row[10]),
			AdRequests:            cast.StringToInt(row[11]),
			ImpressionsCtr:        cast.StringToFloat(row[12]),
			ActiveViewViewability: cast.StringToFloat(row[13]),
			CostPerClick:          cast.StringToFloat(row[14]),
			MatchedAdRequests:     cast.StringToInt(row[15]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.channelCountryRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.channelCountryRepo.CreateHKDData(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}
func (uc *AdsenseUsecase) SaveUrlChannelCountryData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseURLChannelCountryData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseURLChannelCountryData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			UrlChannel:            row[0],
			Country:               row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.urlChannelCountryRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.urlChannelCountryRepo.CreateHKDData(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SaveSiteData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseSiteData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseSiteData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Site:                  row[0],
			EstimatedEarnings:     cast.StringToFloat(row[2]),
			PageViews:             cast.StringToInt(row[3]),
			PageViewsRpm:          cast.StringToFloat(row[4]),
			IMPRESSIONS:           cast.StringToInt(row[5]),
			ImpressionsRpm:        cast.StringToFloat(row[6]),
			AdRequestsCoverage:    cast.StringToFloat(row[7]),
			CLICKS:                cast.StringToInt(row[8]),
			AdRequests:            cast.StringToInt(row[9]),
			ImpressionsCtr:        cast.StringToFloat(row[10]),
			ActiveViewViewability: cast.StringToFloat(row[11]),
			CostPerClick:          cast.StringToFloat(row[12]),
			MatchedAdRequests:     cast.StringToInt(row[13]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		//err := uc.siteRepo.Create(ctx, ascd)
		//if err != nil {
		//	return err
		//}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.siteRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.siteRepo.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}
func (uc *AdsenseUsecase) SaveTodaySiteData(ctx context.Context, data *Adsense) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)

	for _, row := range data.Data {
		ascd := &AdsenseSiteTodayData{
			Date:                  time.Now().In(cstSh),
			Site:                  row[0],
			EstimatedEarnings:     cast.StringToFloat(row[2]),
			PageViews:             cast.StringToInt(row[3]),
			PageViewsRpm:          cast.StringToFloat(row[4]),
			IMPRESSIONS:           cast.StringToInt(row[5]),
			ImpressionsRpm:        cast.StringToFloat(row[6]),
			AdRequestsCoverage:    cast.StringToFloat(row[7]),
			CLICKS:                cast.StringToInt(row[8]),
			AdRequests:            cast.StringToInt(row[9]),
			ImpressionsCtr:        cast.StringToFloat(row[10]),
			ActiveViewViewability: cast.StringToFloat(row[11]),
			CostPerClick:          cast.StringToFloat(row[12]),
			MatchedAdRequests:     cast.StringToInt(row[13]),
		}
		err := uc.tsRepo.Create(ctx, ascd)
		if err != nil {
			return err
		}

	}
	return nil
}

func (uc *AdsenseUsecase) SaveSiteAdFormatData(ctx context.Context, data *Adsense, currencyCode string, saveHistory bool, dateRange string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseSiteAdFormatData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseSiteAdFormatData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Site:                  row[0],
			AdFormat:              row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
			ActiveViewTime:        cast.StringToInt(row[15]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.siteAdFormatRepo.Create(ctx, datas, saveHistory)
		if err != nil {
			return err
		}
	} else {
		err := uc.siteAdFormatRepo.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	if saveHistory {
		err := uc.saveReductionData(ctx, dateRange, cstSh)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) saveReductionData(ctx context.Context, dateRange string, cstSh *time.Location) error {
	s, e := date.GetDateRange(dateRange, time.Now().Local())

	reductionData, err := uc.siteAdFormatRepo.GetSiteReduction(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return err
	}
	var reductions []*AdReduction
	today := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()-1, 16, 0, 0, 0, time.UTC)

	for _, data := range reductionData {
		//RICE
		reductionType := ""
		adRequestsChangePercent := 0.0
		impressionsChangePercent := 0.0
		clicksChangePercent := 0.0
		estimatedEarningsChangePercent := 0.0
		if !data.LatestCollectedAt.Equal(today) {
			continue
		}

		if data.AdRequestsChangePercent != nil {
			adRequestsChangePercent = *data.AdRequestsChangePercent
		}
		if data.ImpressionsChangePercent != nil {
			impressionsChangePercent = *data.ImpressionsChangePercent
		}
		if data.ClicksChangePercent != nil {
			clicksChangePercent = *data.ClicksChangePercent
		}
		if data.EstimatedEarningsChangePercent != nil {
			estimatedEarningsChangePercent = *data.EstimatedEarningsChangePercent
		}
		if data.AdRequestsChange != 0 {
			reductionType += "R"
		}
		if data.ImpressionsChange != 0 {
			reductionType += "I"
		}
		if data.ClicksChange != 0 {
			reductionType += "C"
		}
		if data.EstimatedEarningsChange != 0 {
			reductionType += "E"
		}

		reductions = append(reductions, &AdReduction{
			Date:             data.Date,
			Site:             data.Site,
			ReductionDate:    data.LatestCollectedAt,
			DeltaDay:         int(data.LatestCollectedAt.Sub(data.Date).Hours() / 24),
			ReductionType:    reductionType,
			ReductionRateR:   adRequestsChangePercent,
			ReductionRateI:   impressionsChangePercent,
			ReductionRateC:   clicksChangePercent,
			ReductionRateE:   estimatedEarningsChangePercent,
			ReductionChangeR: data.AdRequestsChange,
			ReductionChangeI: data.ImpressionsChange,
			ReductionChangeC: data.ClicksChange,
			ReductionChangeE: data.EstimatedEarningsChange,
			Account:          data.Account,
			Platform:         data.Platform,
		})
	}
	err = uc.adReductionRepo.Save(ctx, reductions)
	if err != nil {
		return err
	}

	return nil
}

func (uc *AdsenseUsecase) SaveChannelData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseChannelData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[2])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseChannelData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Channel:               row[0],
			ChannelID:             row[1],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.channelRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.channelRepo.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SaveChannelAdFormatData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseChannelAdFormatData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[2])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseChannelAdFormatData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Channel:               row[0],
			ChannelID:             row[1],
			AdFormat:              row[3],
			EstimatedEarnings:     cast.StringToFloat(row[4]),
			PageViews:             cast.StringToInt(row[5]),
			PageViewsRpm:          cast.StringToFloat(row[6]),
			IMPRESSIONS:           cast.StringToInt(row[7]),
			ImpressionsRpm:        cast.StringToFloat(row[8]),
			AdRequestsCoverage:    cast.StringToFloat(row[9]),
			CLICKS:                cast.StringToInt(row[10]),
			AdRequests:            cast.StringToInt(row[11]),
			ImpressionsCtr:        cast.StringToFloat(row[12]),
			ActiveViewViewability: cast.StringToFloat(row[13]),
			CostPerClick:          cast.StringToFloat(row[14]),
			MatchedAdRequests:     cast.StringToInt(row[15]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.channelAdFormatRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.channelAdFormatRepo.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SavePageURLAdFormatData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsensePageURLAdFormatData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsensePageURLAdFormatData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			PageURL:               row[0],
			AdFormat:              row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.pageUrlAdFormat.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.pageUrlAdFormat.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SaveUrlChannelData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseURLChannelData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseURLChannelData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			UrlChannel:            row[0],
			EstimatedEarnings:     cast.StringToFloat(row[2]),
			PageViews:             cast.StringToInt(row[3]),
			PageViewsRpm:          cast.StringToFloat(row[4]),
			IMPRESSIONS:           cast.StringToInt(row[5]),
			ImpressionsRpm:        cast.StringToFloat(row[6]),
			AdRequestsCoverage:    cast.StringToFloat(row[7]),
			CLICKS:                cast.StringToInt(row[8]),
			AdRequests:            cast.StringToInt(row[9]),
			ImpressionsCtr:        cast.StringToFloat(row[10]),
			ActiveViewViewability: cast.StringToFloat(row[11]),
			CostPerClick:          cast.StringToFloat(row[12]),
			MatchedAdRequests:     cast.StringToInt(row[13]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.urlChannelRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.urlChannelRepo.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SaveUrlChannelAdFormatData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseURLChannelAdFormatData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseURLChannelAdFormatData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			UrlChannel:            row[0],
			AdFormat:              row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.urlChannelAdFormatRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.urlChannelAdFormatRepo.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SaveSiteZeroData(ctx context.Context, data *Adsense) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)

	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseSiteTodayData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day()+1, 0, 0, 0, 0, cstSh),
			Site:                  row[0],
			EstimatedEarnings:     cast.StringToFloat(row[2]),
			PageViews:             cast.StringToInt(row[3]),
			PageViewsRpm:          cast.StringToFloat(row[4]),
			IMPRESSIONS:           cast.StringToInt(row[5]),
			ImpressionsRpm:        cast.StringToFloat(row[6]),
			AdRequestsCoverage:    cast.StringToFloat(row[7]),
			CLICKS:                cast.StringToInt(row[8]),
			AdRequests:            cast.StringToInt(row[9]),
			ImpressionsCtr:        cast.StringToFloat(row[10]),
			ActiveViewViewability: cast.StringToFloat(row[11]),
			CostPerClick:          cast.StringToFloat(row[12]),
			MatchedAdRequests:     cast.StringToInt(row[13]),
		}
		err = uc.tsRepo.Create(ctx, ascd)
		if err != nil {
			return err
		}

	}
	return nil
}

func (uc *AdsenseUsecase) SaveSiteCountryZeroData(ctx context.Context, data *Adsense) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)

	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseSiteCountryTodayData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day()+1, 0, 0, 0, 0, cstSh),
			Site:                  row[0],
			Country:               row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
		}
		err = uc.tscRepo.Create(ctx, ascd)
		if err != nil {
			return err
		}

	}
	return nil
}

func (uc *AdsenseUsecase) SaveSiteCountryAdFormatData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseSiteCountryAdFormatData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseSiteCountryAdFormatData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Site:                  row[0],
			AdFormat:              row[2],
			Country:               row[3],
			EstimatedEarnings:     cast.StringToFloat(row[4]),
			PageViews:             cast.StringToInt(row[5]),
			PageViewsRpm:          cast.StringToFloat(row[6]),
			IMPRESSIONS:           cast.StringToInt(row[7]),
			ImpressionsRpm:        cast.StringToFloat(row[8]),
			AdRequestsCoverage:    cast.StringToFloat(row[9]),
			CLICKS:                cast.StringToInt(row[10]),
			AdRequests:            cast.StringToInt(row[11]),
			ImpressionsCtr:        cast.StringToFloat(row[12]),
			ActiveViewViewability: cast.StringToFloat(row[13]),
			CostPerClick:          cast.StringToFloat(row[14]),
			MatchedAdRequests:     cast.StringToInt(row[15]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)
	}
	if currencyCode == USD {
		err := uc.siteCountryAdFormatRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.siteCountryAdFormatRepo.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SaveChannelCountryAdFormatData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseChannelCountryAdFormatData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[2])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseChannelCountryAdFormatData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Channel:               row[0],
			ChannelID:             row[1],
			AdFormat:              row[3],
			Country:               row[4],
			EstimatedEarnings:     cast.StringToFloat(row[5]),
			PageViews:             cast.StringToInt(row[6]),
			PageViewsRpm:          cast.StringToFloat(row[7]),
			IMPRESSIONS:           cast.StringToInt(row[8]),
			ImpressionsRpm:        cast.StringToFloat(row[9]),
			AdRequestsCoverage:    cast.StringToFloat(row[10]),
			CLICKS:                cast.StringToInt(row[11]),
			AdRequests:            cast.StringToInt(row[12]),
			ImpressionsCtr:        cast.StringToFloat(row[13]),
			ActiveViewViewability: cast.StringToFloat(row[14]),
			CostPerClick:          cast.StringToFloat(row[15]),
			MatchedAdRequests:     cast.StringToInt(row[16]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)
	}
	if currencyCode == USD {
		err := uc.channelCountryAdFormatRepo.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.channelCountryAdFormatRepo.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) SaveUrlChannelCountryAdFormatData(ctx context.Context, data *Adsense, currencyCode string, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseURLChannelCountryAdFormatData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseURLChannelCountryAdFormatData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			UrlChannel:            row[0],
			AdFormat:              row[2],
			Country:               row[3],
			EstimatedEarnings:     cast.StringToFloat(row[4]),
			PageViews:             cast.StringToInt(row[5]),
			PageViewsRpm:          cast.StringToFloat(row[6]),
			IMPRESSIONS:           cast.StringToInt(row[7]),
			ImpressionsRpm:        cast.StringToFloat(row[8]),
			AdRequestsCoverage:    cast.StringToFloat(row[9]),
			CLICKS:                cast.StringToInt(row[10]),
			AdRequests:            cast.StringToInt(row[11]),
			ImpressionsCtr:        cast.StringToFloat(row[12]),
			ActiveViewViewability: cast.StringToFloat(row[13]),
			CostPerClick:          cast.StringToFloat(row[14]),
			MatchedAdRequests:     cast.StringToInt(row[15]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	if currencyCode == USD {
		err := uc.urlChannelCountryAdFormat.Create(ctx, datas)
		if err != nil {
			return err
		}
	} else {
		err := uc.urlChannelCountryAdFormat.CreateHKD(ctx, datas)
		if err != nil {
			return err
		}
	}

	return nil
}

func (uc *AdsenseUsecase) GenerateExcel(metrics []string, data []map[string]interface{}) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			return
		}
	}()
	_, err := f.NewSheet(ExcelSheetReport)
	if err != nil {
		return nil, err
	}
	err = f.DeleteSheet(ExcelSheetDefault)
	if err != nil {
		return nil, err
	}
	sw, err := f.NewStreamWriter(ExcelSheetReport)
	if err != nil {
		return nil, err
	}
	row := make([]interface{}, len(metrics))
	for ci, metric := range metrics {
		row[ci] = i18n.T(metric)
	}
	if err := sw.SetRow("A1", row); err != nil {
		return nil, err
	}
	for rowID := 0; rowID < len(data); rowID++ {
		row := make([]interface{}, len(metrics))
		for ci, metric := range metrics {
			if metric == FieldName {
				metric = FieldCooperation
			}
			if d, ok := data[rowID][metric].(string); ok {
				row[ci] = i18n.T(d)
			} else {
				row[ci] = data[rowID][metric]
			}
		}
		cell, err := excelize.CoordinatesToCellName(1, rowID+2)
		if err != nil {
			return nil, err
		}
		if err := sw.SetRow(cell, row); err != nil {
			return nil, err
		}
	}
	if err := sw.Flush(); err != nil {
		return nil, err
	}
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return nil, err
	}
	return buffer, nil
}

type SendInfo struct {
	// 报告名称
	ReportName string
	// 时间范围
	DateRange string
	// 发送时间
	SendTime string
}

func (uc *AdsenseUsecase) SendEmailAddAttach(ctx context.Context, excel *bytes.Buffer, sendInfo *SendInfo) error {
	err := uc.email.SendEmailAddAttach(ctx, uc.nvTo, uc.nvCC, sendInfo.ReportName, sendInfo.DateRange, sendInfo.SendTime, excel)
	if err != nil {
		return err
	}
	return nil
}
func (uc *AdsenseUsecase) SendEmail(ctx context.Context, data *bytes.Buffer, sendInfo *SendInfo) error {
	err := uc.email.SendEmail(ctx, uc.djsTo, sendInfo.ReportName, sendInfo.DateRange, sendInfo.SendTime, data)
	if err != nil {
		return err
	}
	return nil
}
func (uc *AdsenseUsecase) SaveNovabeyondPageUrlCountryData(ctx context.Context, metrics []string, data *Adsense) ([]map[string]interface{}, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]map[string]interface{}, 0)

	for _, row := range data.Data {
		result := make(map[string]interface{})

		date, err := time.Parse(time.DateOnly, row[0])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return datas, err
		}
		for _, m := range metrics {
			switch m {
			case "PAGE_URL":
				result[m] = row[1]
			case "COUNTRY_NAME":
				result[m] = row[2]
			case "DATE":
				result[m] = time.Date(date.Year(), date.Month(), date.Day(), time.Now().In(cstSh).Hour()-1, 0, 0, 0, cstSh).Format(time.DateTime)
			case "ESTIMATED_EARNINGS":
				result[m] = row[3]
			case "PAGE_VIEWS":
				result[m] = row[11]
			case "PAGE_VIEWS_RPM":
				result[m] = row[12]
			case "IMPRESSIONS":
				result[m] = row[4]
			case "IMPRESSIONS_RPM":
				result[m] = row[9]
			case "AD_REQUESTS_COVERAGE":
				result[m] = row[10]
			case "CLICKS":
				result[m] = row[6]
			case "AD_REQUESTS":
				result[m] = row[5]
			case "IMPRESSIONS_CTR":
				result[m] = row[7]
			case "COST_PER_CLICK":
				result[m] = row[8]
			}
		}
		datas = append(datas, result)
	}

	return datas, nil
}

func (uc *AdsenseUsecase) SaveNovabeyondUrlChannelCountryData(ctx context.Context, metrics []string, data *Adsense) ([]map[string]interface{}, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]map[string]interface{}, 0)

	for _, row := range data.Data {
		result := make(map[string]interface{})

		date, err := time.Parse(time.DateOnly, row[0])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return datas, err
		}
		for _, m := range metrics {
			switch m {
			case "URL_CHANNEL_NAME":
				result[m] = row[1]
			case "COUNTRY_NAME":
				result[m] = row[2]
			case "DATE":
				result[m] = time.Date(date.Year(), date.Month(), date.Day(), time.Now().In(cstSh).Hour()-1, 0, 0, 0, cstSh).Format(time.DateTime)
			case "ESTIMATED_EARNINGS":
				result[m] = row[3]
			case "PAGE_VIEWS":
				result[m] = row[11]
			case "PAGE_VIEWS_RPM":
				result[m] = row[12]
			case "IMPRESSIONS":
				result[m] = row[4]
			case "IMPRESSIONS_RPM":
				result[m] = row[9]
			case "AD_REQUESTS_COVERAGE":
				result[m] = row[10]
			case "CLICKS":
				result[m] = row[6]
			case "AD_REQUESTS":
				result[m] = row[5]
			case "IMPRESSIONS_CTR":
				result[m] = row[7]
			case "COST_PER_CLICK":
				result[m] = row[8]
			}
		}
		datas = append(datas, result)
	}

	return datas, nil
}

func (uc *AdsenseUsecase) GenerateCsv(metrics []string, data []map[string]interface{}) (*bytes.Buffer, error) {
	buffer := &bytes.Buffer{}
	csvWriter := csv.NewWriter(buffer)

	header := make([]string, len(metrics))
	for i, metric := range metrics {
		header[i] = i18n.T(metric)
	}
	if err := csvWriter.Write(header); err != nil {
		return nil, err
	}

	for _, row := range data {
		csvRow := make([]string, len(metrics))
		for i, metric := range metrics {
			value, ok := row[metric]
			if !ok {
				return nil, errors.New("missing metric: " + metric)
			}
			switch v := value.(type) {
			case string:
				csvRow[i] = i18n.T(v)
			case int:
				csvRow[i] = strconv.Itoa(v)
			case float64:
				csvRow[i] = strconv.FormatFloat(v, 'f', -1, 64)
			default:
				return nil, errors.New("unsupported data type for metric: " + metric)
			}
		}
		if err := csvWriter.Write(csvRow); err != nil {
			return nil, err
		}
	}

	csvWriter.Flush()
	if err := csvWriter.Error(); err != nil {
		return nil, err
	}

	return buffer, nil
}

func (uc *AdsenseUsecase) SaveYesterdayNovabeyondUrlChannelCountryData(ctx context.Context, metrics []string, data *Adsense) ([]map[string]interface{}, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]map[string]interface{}, 0)

	for _, row := range data.Data {
		result := make(map[string]interface{})

		date, err := time.Parse(time.DateOnly, row[0])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return datas, err
		}
		for _, m := range metrics {
			switch m {
			case "URL_CHANNEL_NAME":
				result[m] = row[1]
			case "COUNTRY_NAME":
				result[m] = row[2]
			case "DATE":
				result[m] = time.Date(date.Year(), date.Month(), date.Day(), 23, 0, 0, 0, cstSh).Format(time.DateTime)
			case "ESTIMATED_EARNINGS":
				result[m] = row[3]
			case "PAGE_VIEWS":
				result[m] = row[11]
			case "PAGE_VIEWS_RPM":
				result[m] = row[12]
			case "IMPRESSIONS":
				result[m] = row[4]
			case "IMPRESSIONS_RPM":
				result[m] = row[9]
			case "AD_REQUESTS_COVERAGE":
				result[m] = row[10]
			case "CLICKS":
				result[m] = row[6]
			case "AD_REQUESTS":
				result[m] = row[5]
			case "IMPRESSIONS_CTR":
				result[m] = row[7]
			case "COST_PER_CLICK":
				result[m] = row[8]
			}
		}
		datas = append(datas, result)
	}

	return datas, nil
}

func (uc *AdsenseUsecase) SaveNovabeyondYestPageUrlCountryData(ctx context.Context, metrics []string, data *Adsense) ([]map[string]interface{}, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]map[string]interface{}, 0)

	for _, row := range data.Data {
		result := make(map[string]interface{})

		date, err := time.Parse(time.DateOnly, row[0])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return datas, err
		}
		for _, m := range metrics {
			switch m {
			case "PAGE_URL":
				result[m] = row[1]
			case "COUNTRY_NAME":
				result[m] = row[2]
			case "DATE":
				result[m] = time.Date(date.Year(), date.Month(), date.Day(), 23, 0, 0, 0, cstSh).Format(time.DateTime)
			case "ESTIMATED_EARNINGS":
				result[m] = row[3]
			case "PAGE_VIEWS":
				result[m] = row[11]
			case "PAGE_VIEWS_RPM":
				result[m] = row[12]
			case "IMPRESSIONS":
				result[m] = row[4]
			case "IMPRESSIONS_RPM":
				result[m] = row[9]
			case "AD_REQUESTS_COVERAGE":
				result[m] = row[10]
			case "CLICKS":
				result[m] = row[6]
			case "AD_REQUESTS":
				result[m] = row[5]
			case "IMPRESSIONS_CTR":
				result[m] = row[7]
			case "COST_PER_CLICK":
				result[m] = row[8]
			}
		}
		datas = append(datas, result)
	}

	return datas, nil
}
func (uc *AdsenseUsecase) ListChannelCountriesHKDData(ctx context.Context, s time.Time, e time.Time) (map[string]int64, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	list, err := uc.channelCountryRepo.ListHKD(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}

	m := make(map[string]int64)
	for _, l := range list {
		m[fmt.Sprintf("%s-%s-%s-%s-%s", l.Channel, l.Country, l.Date.Format(time.DateOnly), l.Platform, l.Account)] = int64(l.PageViews)
	}

	return m, nil
}

func (uc *AdsenseUsecase) ListChannelCountriesData(ctx context.Context, s time.Time, e time.Time) (map[string]int64, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	list, err := uc.channelCountryRepo.List(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}

	m := make(map[string]int64)
	for _, l := range list {
		m[fmt.Sprintf("%s-%s-%s-%s-%s", l.Channel, l.Country, l.Date.Format(time.DateOnly), l.Platform, l.Account)] = int64(l.PageViews)
	}

	return m, nil
}
func (uc *AdsenseUsecase) ListHKDChannels(ctx context.Context, s, e time.Time) (map[string]int64, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	list, err := uc.channelRepo.ListHKD(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}

	m := make(map[string]int64)
	for _, l := range list {
		m[l.Channel+l.Platform+l.Account] = int64(l.PageViews)
	}

	return m, nil
}
func (uc *AdsenseUsecase) ListChannelCountryAdFormatsHKD(ctx context.Context, s time.Time, e time.Time) (map[string]*AdsenseChannelCountryAdFormatData, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	list, err := uc.channelCountryAdFormatRepo.ListHKD(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}
	m := make(map[string]*AdsenseChannelCountryAdFormatData)
	for _, l := range list {
		m[fmt.Sprintf("%s-%s-%s-%s-%s", l.Channel, l.Country, l.Date.Format(time.DateOnly), l.Platform, l.Account)] = l
	}
	return m, nil
}

func (uc *AdsenseUsecase) ListChannels(ctx context.Context, s, e time.Time) (map[string]int64, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	list, err := uc.channelRepo.List(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}

	m := make(map[string]int64)
	for _, l := range list {
		m[l.Channel+l.Platform+l.Account] = int64(l.PageViews)
	}

	return m, nil
}
func (uc *AdsenseUsecase) ListChannelCountryAdFormats(ctx context.Context, s time.Time, e time.Time) (map[string]*AdsenseChannelCountryAdFormatData, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	list, err := uc.channelCountryAdFormatRepo.List(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}
	m := make(map[string]*AdsenseChannelCountryAdFormatData)
	for _, l := range list {
		m[fmt.Sprintf("%s-%s-%s-%s-%s", l.Channel, l.Country, l.Date.Format(time.DateOnly), l.Platform, l.Account)] = l
	}
	return m, nil
}
func (uc *AdsenseUsecase) ListHKDChannelAdFormats(ctx context.Context, s, e time.Time) (map[string]*AdsenseChannelAdFormatData, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	list, err := uc.channelAdFormatRepo.ListHKD(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}
	m := make(map[string]*AdsenseChannelAdFormatData)
	for _, l := range list {
		m[l.Channel+l.Platform+l.Account] = l
	}
	return m, nil
}
func (uc *AdsenseUsecase) ListChannelAdFormats(ctx context.Context, s, e time.Time) (map[string]*AdsenseChannelAdFormatData, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	list, err := uc.channelAdFormatRepo.List(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}
	m := make(map[string]*AdsenseChannelAdFormatData)
	for _, l := range list {
		m[l.Channel+l.Platform+l.Account] = l
	}
	return m, nil
}

func (uc *AdsenseUsecase) MergeChannelsCountryHKD(ctx context.Context, pvs map[string]int64, formatsMap map[string]*AdsenseChannelCountryAdFormatData) error {
	datas := make([]*AdsenseChannelCountryData, 0)
	for k, row := range pvs {
		adData := formatsMap[k]
		if adData == nil {
			continue
		}
		pageRpm := 0.0
		if row != 0 {
			pageRpm = adData.EstimatedEarnings / float64(row)
		}
		ascd := &AdsenseChannelCountryData{
			Date:                  adData.Date,
			Channel:               adData.Channel,
			ChannelID:             adData.ChannelID,
			EstimatedEarnings:     adData.EstimatedEarnings,
			PageViews:             int(row),
			PageViewsRpm:          pageRpm,
			IMPRESSIONS:           adData.IMPRESSIONS,
			ImpressionsRpm:        adData.ImpressionsRpm,
			AdRequestsCoverage:    adData.AdRequestsCoverage,
			CLICKS:                adData.CLICKS,
			AdRequests:            adData.AdRequests,
			ImpressionsCtr:        adData.ImpressionsCtr,
			ActiveViewViewability: adData.ActiveViewViewability,
			CostPerClick:          adData.CostPerClick,
			MatchedAdRequests:     adData.MatchedAdRequests,
			Country:               adData.Country,
			Account:               adData.Account,
			Platform:              adData.Platform,
		}
		datas = append(datas, ascd)
	}
	err := uc.gameCountryRepo.CreateHKD(ctx, datas)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) MergeChannelsCountry(ctx context.Context, pvs map[string]int64, formatsMap map[string]*AdsenseChannelCountryAdFormatData) error {
	datas := make([]*AdsenseChannelCountryData, 0)
	for k, row := range pvs {
		adData := formatsMap[k]
		if adData == nil {
			continue
		}
		pageRpm := 0.0
		if row != 0 {
			pageRpm = adData.EstimatedEarnings / float64(row)
		}
		ascd := &AdsenseChannelCountryData{
			Date:                  adData.Date,
			Channel:               adData.Channel,
			ChannelID:             adData.ChannelID,
			EstimatedEarnings:     adData.EstimatedEarnings,
			PageViews:             int(row),
			PageViewsRpm:          pageRpm,
			IMPRESSIONS:           adData.IMPRESSIONS,
			ImpressionsRpm:        adData.ImpressionsRpm,
			AdRequestsCoverage:    adData.AdRequestsCoverage,
			CLICKS:                adData.CLICKS,
			AdRequests:            adData.AdRequests,
			ImpressionsCtr:        adData.ImpressionsCtr,
			ActiveViewViewability: adData.ActiveViewViewability,
			CostPerClick:          adData.CostPerClick,
			MatchedAdRequests:     adData.MatchedAdRequests,
			Country:               adData.Country,
			Account:               adData.Account,
			Platform:              adData.Platform,
		}
		datas = append(datas, ascd)
	}
	err := uc.gameCountryRepo.Create(ctx, datas)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) MergeChannels(ctx context.Context, pvs map[string]int64, formatsMap map[string]*AdsenseChannelAdFormatData) error {
	datas := make([]*AdsenseChannelData, 0)
	for k, row := range pvs {
		adData := formatsMap[k]
		if adData == nil {
			continue
		}
		pageRpm := 0.0
		if row != 0 {
			pageRpm = adData.EstimatedEarnings / float64(row)
		}
		ascd := &AdsenseChannelData{
			Date:                  adData.Date,
			Channel:               adData.Channel,
			ChannelID:             adData.ChannelID,
			EstimatedEarnings:     adData.EstimatedEarnings,
			PageViews:             int(row),
			PageViewsRpm:          pageRpm,
			IMPRESSIONS:           adData.IMPRESSIONS,
			ImpressionsRpm:        adData.ImpressionsRpm,
			AdRequestsCoverage:    adData.AdRequestsCoverage,
			CLICKS:                adData.CLICKS,
			AdRequests:            adData.AdRequests,
			ImpressionsCtr:        adData.ImpressionsCtr,
			ActiveViewViewability: adData.ActiveViewViewability,
			CostPerClick:          adData.CostPerClick,
			MatchedAdRequests:     adData.MatchedAdRequests,
			Account:               adData.Account,
			Platform:              adData.Platform,
		}
		datas = append(datas, ascd)
	}
	err := uc.gameRepo.Create(ctx, datas)
	if err != nil {
		return err
	}
	return nil
}
func (uc *AdsenseUsecase) MergeHKDChannels(ctx context.Context, pvs map[string]int64, formatsMap map[string]*AdsenseChannelAdFormatData) error {
	datas := make([]*AdsenseChannelData, 0)
	for k, row := range pvs {
		adData := formatsMap[k]
		if adData == nil {
			continue
		}
		pageRpm := 0.0
		if row != 0 {
			pageRpm = adData.EstimatedEarnings / float64(row)
		}
		ascd := &AdsenseChannelData{
			Date:                  adData.Date,
			Channel:               adData.Channel,
			ChannelID:             adData.ChannelID,
			EstimatedEarnings:     adData.EstimatedEarnings,
			PageViews:             int(row),
			PageViewsRpm:          pageRpm,
			IMPRESSIONS:           adData.IMPRESSIONS,
			ImpressionsRpm:        adData.ImpressionsRpm,
			AdRequestsCoverage:    adData.AdRequestsCoverage,
			CLICKS:                adData.CLICKS,
			AdRequests:            adData.AdRequests,
			ImpressionsCtr:        adData.ImpressionsCtr,
			ActiveViewViewability: adData.ActiveViewViewability,
			CostPerClick:          adData.CostPerClick,
			MatchedAdRequests:     adData.MatchedAdRequests,
			Account:               adData.Account,
			Platform:              adData.Platform,
		}
		datas = append(datas, ascd)
	}
	err := uc.gameRepo.CreateHKD(ctx, datas)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) SaveCustomChannelData(ctx context.Context, data []*AdsenseCustomChannel) error {
	err := uc.customChannelRepo.CreateOrUpdate(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) SendMsg(ctx context.Context, msg string) error {
	return uc.bot.SendMarkdown(msg)
}

func (uc *AdsenseUsecase) GetAdsenseUrlChannelData(ctx context.Context, pubID string) ([]*AdsenseUrlChannel, error) {
	channels, err := uc.repo.ListUrlChannels(ctx, pubID)
	if err != nil {
		return nil, err
	}
	return channels, nil
}

// GetAllPubIDs 多账号支持方法
func (uc *AdsenseUsecase) GetAllPubIDs() []string {
	return uc.repo.GetAllPubIDs()
}

func (uc *AdsenseUsecase) GetPubIDsForJob(jobConfig *conf.Job_JobConfig) ([]string, error) {
	return uc.repo.GetPubIDsForJob(jobConfig)
}

// GetAdSenseDataMultiAccount 多账号数据采集方法
func (uc *AdsenseUsecase) GetAdSenseDataMultiAccount(ctx context.Context, pubIDs []string, metrics, orderBy, dimensions, filters []string, dateRange, currencyCode string) (map[string]*Adsense, error) {
	results := make(map[string]*Adsense)

	for _, pubID := range pubIDs {
		data, err := uc.repo.Generate(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
		if err != nil {
			uc.log.Errorf("获取账号 %s 的数据失败: %v", pubID, err)
			continue // 单个账号失败不影响其他账号
		}
		results[pubID] = data
	}

	return results, nil
}

func (uc *AdsenseUsecase) GetAdSenseDataByCsvMultiAccount(ctx context.Context, pubIDs []string, metrics, orderBy, dimensions, filters []string, dateRange, currencyCode string) (map[string]*Adsense, error) {
	results := make(map[string]*Adsense)

	for _, pubID := range pubIDs {
		data, err := uc.repo.GenerateCsv(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
		if err != nil {
			uc.log.Errorf("获取账号 %s 的CSV数据失败: %v", pubID, err)
			continue
		}
		results[pubID] = data
	}

	return results, nil
}

func (uc *AdsenseUsecase) GetCooperationSite(ctx context.Context) (map[string]string, error) {
	site, err := uc.csucRepo.ListCooperationSite(ctx)
	if err != nil {
		return nil, err
	}

	m := make(map[string]string)
	for _, s := range site {
		m[s.Value().Site] = s.Value().CooperationChannel
	}

	return m, nil
}

func (uc *AdsenseUsecase) SaveCooperationSiteUrlChannel(ctx context.Context, data []*AdsenseUrlChannel, site map[string]string) error {
	datas := make([]CooperationSiteUrlChannelValue, 0)
	for _, c := range data {
		split := strings.Split(c.UriPattern, "/")
		name := split[0]
		if len(strings.Split(split[0], ".")) >= 3 {
			name = strings.Split(split[0], ".")[0]
		}
		if _, ok := site[split[0]]; !ok {
			site[split[0]] = StatusUnknown
		}
		d := CooperationSiteUrlChannelValue{
			Name:               name,
			State:              StatusEnabled,
			Site:               split[0],
			UrlChannel:         c.UriPattern,
			CooperationChannel: site[split[0]],
		}
		datas = append(datas, d)
	}
	err := uc.csucRepo.CreateOrUpdate(ctx, datas)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) CreateOrUpdateCooperation(ctx context.Context) error {
	list, err := uc.csucRepo.List(ctx)
	if err != nil {
		return err
	}
	type count struct {
		site       []string
		urlChannel []string
	}
	m := make(map[string]*count)
	for _, l := range list {
		if m[l.Value().CooperationChannel] == nil {
			m[l.Value().CooperationChannel] = &count{
				site:       make([]string, 0),
				urlChannel: make([]string, 0),
			}
		}
		if l.Value().UrlChannel == "" && l.Value().State == StatusEnabled {
			m[l.Value().CooperationChannel].site = append(m[l.Value().CooperationChannel].site, l.Value().Site)
		}
		if l.Value().UrlChannel != "" && l.Value().State == StatusEnabled {
			m[l.Value().CooperationChannel].urlChannel = append(m[l.Value().CooperationChannel].urlChannel, l.Value().UrlChannel)
		}
	}
	datas := make([]CooperationValue, 0)
	cooperation := make([]string, 0)
	for k, v := range m {
		cooperation = append(cooperation, k)

		d := CooperationValue{
			Name:            k,
			SiteCount:       int32(len(slice.RemoveDuplicates(v.site))),
			UrlChannelCount: int32(len(slice.RemoveDuplicates(v.urlChannel))),
		}
		datas = append(datas, d)
	}
	err = uc.cRepo.CreateOrUpdate(ctx, datas)
	if err != nil {
		return err
	}
	//err = uc.cRepo.DeleteUnusedCooperation(ctx, cooperation)
	//if err != nil {
	//	return err
	//}
	return nil
}

func (uc *AdsenseUsecase) SaveNoCooperationSite(ctx context.Context) error {
	site, err := uc.siteRepo.ListNoCooperationSite(ctx)
	if err != nil {
		return err
	}
	datas := make([]CooperationSiteUrlChannelValue, 0)

	for _, c := range site {
		name := c
		if len(strings.Split(strings.Split(c, "/")[0], ".")) >= 3 {
			name = strings.Split(strings.Split(c, "/")[0], ".")[0]
		}
		d := CooperationSiteUrlChannelValue{
			Name:               name,
			State:              StatusEnabled,
			Site:               c,
			CooperationChannel: StatusUnknown,
		}
		datas = append(datas, d)
	}
	err = uc.csucRepo.CreateOrUpdate(ctx, datas)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) SaveAppCountrySummary(ctx context.Context) error {
	err := uc.appCountrySummaryRepo.Delete(ctx)
	if err != nil {
		return err
	}
	data, err := uc.gameCountryRepo.GetAppCountrySummary(ctx)
	if err != nil {
		return err
	}

	err = uc.appCountrySummaryRepo.Save(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) GetDjsSiteAdFormatCountryData(ctx context.Context, metrics []string, dimensions []string) ([]map[string]interface{}, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	now := time.Now().In(cstSh)
	return uc.siteCountryAdFormatRepo.Get(ctx, metrics, dimensions, uc.djsSite, DateRange{
		Start: time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, cstSh),
		End:   time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, cstSh),
	})
}

func (uc *AdsenseUsecase) FetchEmailData(ctx context.Context, number uint32) error {
	seqSet := imap.SeqSetNum(number)
	fetchOptions := &imap.FetchOptions{
		UID:         true,
		Flags:       true,
		BodySection: []*imap.FetchItemBodySection{{}},
	}

	fetchC, err := imapclient.DialTLS(uc.imapConfig.GetAddress(), &imapclient.Options{
		WordDecoder: &mime.WordDecoder{CharsetReader: charset.Reader},
		DebugWriter: os.Stdout,
	})
	if err != nil {
		return err
	}
	defer fetchC.Close()
	err = fetchC.WaitGreeting()
	if err != nil {
		return err
	}
	if err := fetchC.Login(uc.imapConfig.GetUsername(), uc.imapConfig.GetPassword()).Wait(); err != nil {
		return err
	}
	if _, err := fetchC.Select(EmailInbox, nil).Wait(); err != nil {
		return err
	}
	//searchData, err := fetchC.Search(&imap.SearchCriteria{
	//	Flag: []imap.Flag{imap.FlagSeen},
	//}, &imap.SearchOptions{
	//	ReturnAll: true,
	//}).Wait()
	//if err != nil {
	//	return err
	//}
	//for _, sn := range searchData.AllSeqNums() {
	//	if sn == number {
	//		uc.log.Infof("search fonud %d", number)
	//		return nil
	//	}
	//}
	//uc.log.Infof("search done")
	fetchCmd := fetchC.Fetch(seqSet, fetchOptions)
	defer fetchCmd.Close()
	for {
		msg := fetchCmd.Next()
		if msg == nil {
			//// 将邮件设为已读
			storeFlags := imap.StoreFlags{
				Op:     imap.StoreFlagsAdd,
				Flags:  []imap.Flag{imap.FlagSeen},
				Silent: true,
			}
			if err := fetchC.Store(seqSet, &storeFlags, nil).Close(); err != nil {
				return err
			}
			break
		}

		for {
			item := msg.Next()
			if item == nil {
				break
			}
			switch item := item.(type) {
			case imapclient.FetchItemDataFlags:
				//uc.log.Infof("邮件标记: %v \n", item.Flags)
				//for _, f := range item.Flags {
				//	if f == imap.FlagSeen {
				//		return nil
				//	}
				//}
			case imapclient.FetchItemDataUID:
			case imapclient.FetchItemDataBodySection:
				mr, err := mail.CreateReader(item.Literal)
				if err != nil {
					uc.log.Errorf("邮件读取时出现错误： %v \n", err)
					return err
				}
				subject, err := mr.Header.Subject()
				if err != nil {
					uc.log.Errorf("邮件主题 读取错误: %v", err)
					return err
				}
				if subject != EmailSubjectTechData {
					break
				}

				for {
					p, err := mr.NextPart()
					if err == io.EOF {
						break
					} else if err != nil {
						uc.log.Errorf("读取邮件内容时出现错误：%v \n", err)
						return err
					}

					switch h := p.Header.(type) {
					case *mail.InlineHeader:
					case *mail.AttachmentHeader:
						filename, _ := h.Filename()
						uc.log.Infof("得到附件: %v \n", filename)
						err := uc.saveAttachment(ctx, p.Body)
						if err != nil {
							return err
						}
					}
				}
				uc.log.Infof("一封邮件读取完毕\n")
				uc.log.Infof("------------------------- \n\n")
			}
		}
	}

	return nil
}

type DateRange struct {
	Start time.Time
	End   time.Time
}

func (uc *AdsenseUsecase) saveAttachment(ctx context.Context, body io.Reader) error {
	data := make([]*AdManagerSite, 0)

	csvReader := csv.NewReader(body)
	// 避免每行字段不一致，导致报错
	csvReader.FieldsPerRecord = -1
	line := 0
	for {
		rec, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		line++
		if line <= 9 {
			continue
		}
		if rec[0] == CSVTotalLabel {
			continue
		}
		date, err := time.Parse(DateFormatYMD, rec[0])
		if err != nil {
			return err
		}
		data = append(data, &AdManagerSite{
			Date:                  date,
			ChildNetworkCode:      rec[1],
			Site:                  rec[2],
			ChildNetworkID:        rec[3],
			AdExchangeImpressions: cast.StringToInt(strings.ReplaceAll(rec[4], ",", "")),
			AdExchangeClicks:      cast.StringToInt(strings.ReplaceAll(rec[5], ",", "")),
			AdExchangeCtr:         cast.StringToFloat(strings.Split(rec[6], "%")[0]) / 100.0,
			AdExchangeRevenue:     cast.StringToFloat(strings.ReplaceAll(rec[7], ",", "")),
			AdExchangeAverageEcpm: cast.StringToFloat(strings.ReplaceAll(rec[8], ",", "")),
			AdExchangeAdRequests:  cast.StringToInt(strings.ReplaceAll(rec[9], ",", "")),
			AdExchangeMatchRate:   cast.StringToFloat(strings.Split(rec[10], "%")[0]) / 100.0,
		})
		// do something with read line
	}
	err := uc.adManagerSiteRepo.Save(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) SaveBusinessSiteData(ctx context.Context, s time.Time, e time.Time) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	data, err := uc.siteAdFormatRepo.ListBusinessSiteData(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return err
	}

	err = uc.businessSiteRepo.Create(ctx, data)
	if err != nil {
		return err
	}

	// 清理已下架网站的历史数据
	err = uc.businessSiteRepo.CleanupStoppedSiteData(ctx)
	if err != nil {
		// 记录错误但不影响主流程
		uc.log.WithContext(ctx).Errorf("清理已下架网站历史数据失败: %v", err)
	}

	return nil
}

func (uc *AdsenseUsecase) ListSites(ctx context.Context, start time.Time, end time.Time) (map[string]*AdsenseSiteData, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	data, err := uc.siteRepo.Query(ctx, start.In(cstSh), end.In(cstSh))
	if err != nil {
		return nil, err
	}
	siteDataMap := make(map[string]*AdsenseSiteData)
	for _, site := range data {
		siteDataMap[fmt.Sprintf("%s-%s", site.Date.Format(time.DateOnly), site.Site)] = site
	}
	return siteDataMap, nil
}

func (uc *AdsenseUsecase) ListAdManagerSites(ctx context.Context, start time.Time, end time.Time) (map[string]*AdManagerSite, error) {
	data, err := uc.adManagerSiteRepo.Query(ctx, start, end)
	if err != nil {
		return nil, err
	}
	siteDataMap := make(map[string]*AdManagerSite)
	for _, site := range data {
		siteDataMap[fmt.Sprintf("%s-%s", site.Date.Format(time.DateOnly), site.Site)] = site
	}
	return siteDataMap, nil
}

func (uc *AdsenseUsecase) MergeSites(ctx context.Context, adsenseSiteData map[string]*AdsenseSiteData, adManagerSiteData map[string]*AdManagerSite) error {
	commonKeys, uniqueKeys := getSiteDifferences(adsenseSiteData, adManagerSiteData)
	mergedData := make([]*AdsenseSiteData, 0)
	for _, key := range commonKeys {
		mergedData = append(mergedData, &AdsenseSiteData{
			Date:              adManagerSiteData[key].Date,
			Site:              adsenseSiteData[key].Site,
			EstimatedEarnings: adsenseSiteData[key].EstimatedEarnings + adManagerSiteData[key].AdExchangeRevenue,
			IMPRESSIONS:       adsenseSiteData[key].IMPRESSIONS + adManagerSiteData[key].AdExchangeImpressions,
			CLICKS:            adsenseSiteData[key].CLICKS + adManagerSiteData[key].AdExchangeClicks,
			AdRequests:        adsenseSiteData[key].AdRequests + adManagerSiteData[key].AdExchangeAdRequests,
			ImpressionsCtr:    (adsenseSiteData[key].ImpressionsCtr + adManagerSiteData[key].AdExchangeCtr) / 2.0,
			ImpressionsRpm:    (adsenseSiteData[key].ImpressionsRpm + adManagerSiteData[key].AdExchangeAverageEcpm) / 2.0,
		})
	}
	for _, key := range uniqueKeys {
		if data, ok := adsenseSiteData[key]; ok {
			mergedData = append(mergedData, &AdsenseSiteData{
				Date:              data.Date,
				Site:              data.Site,
				EstimatedEarnings: data.EstimatedEarnings,
				IMPRESSIONS:       data.IMPRESSIONS,
				CLICKS:            data.CLICKS,
				AdRequests:        data.AdRequests,
				ImpressionsCtr:    data.ImpressionsCtr,
				ImpressionsRpm:    data.ImpressionsRpm,
			})
		}
		if data, ok := adManagerSiteData[key]; ok {
			mergedData = append(mergedData, &AdsenseSiteData{
				Date:              data.Date,
				Site:              data.Site,
				EstimatedEarnings: data.AdExchangeRevenue,
				IMPRESSIONS:       data.AdExchangeImpressions,
				CLICKS:            data.AdExchangeClicks,
				AdRequests:        data.AdExchangeAdRequests,
				ImpressionsCtr:    data.AdExchangeCtr,
				ImpressionsRpm:    data.AdExchangeAverageEcpm,
			})
		}
	}
	err := uc.unifiedSiteRepo.Create(ctx, mergedData)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) SaveUnifiedSite(ctx context.Context, data *Adsense) error {
	datas := make([]*AdsenseSiteData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseSiteData{
			Date:                  date,
			Site:                  row[0],
			EstimatedEarnings:     cast.StringToFloat(row[2]),
			PageViews:             cast.StringToInt(row[3]),
			PageViewsRpm:          cast.StringToFloat(row[4]),
			IMPRESSIONS:           cast.StringToInt(row[5]),
			ImpressionsRpm:        cast.StringToFloat(row[6]),
			AdRequestsCoverage:    cast.StringToFloat(row[7]),
			CLICKS:                cast.StringToInt(row[8]),
			AdRequests:            cast.StringToInt(row[9]),
			ImpressionsCtr:        cast.StringToFloat(row[10]),
			ActiveViewViewability: cast.StringToFloat(row[11]),
			CostPerClick:          cast.StringToFloat(row[12]),
			MatchedAdRequests:     cast.StringToInt(row[13]),
		}
		datas = append(datas, ascd)

	}
	err := uc.unifiedSiteRepo.Create(ctx, datas)
	if err != nil {
		return err
	}

	return nil
}

func (uc *AdsenseUsecase) SaveTgAd(ctx context.Context) error {
	countries, err := uc.countryRepo.GetCountries(ctx)
	if err != nil {
		return err
	}
	return uc.tgAdRepo.Save(ctx, countries)
}

func (uc *AdsenseUsecase) SaveSiteAdFormatHistoryData(ctx context.Context, data *Adsense) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseSiteAdFormatData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseSiteAdFormatData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Site:                  row[0],
			AdFormat:              row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
			ActiveViewTime:        cast.StringToInt(row[15]),
		}
		datas = append(datas, ascd)

	}
	err := uc.siteAdFormatRepo.CreateHistory(ctx, datas)
	if err != nil {
		return err
	}

	return nil
}

// GetMaxSiteEstimatedEarnings 获取指定时间范围内每个站点每天的最大收益汇总
func (uc *AdsenseUsecase) GetMaxSiteEstimatedEarnings(ctx context.Context, s time.Time, e time.Time) (map[string]float64, error) {

	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	estimatedEarningsData, err := uc.siteAdFormatRepo.GetMaxSiteEstimatedEarnings(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}
	m := make(map[string]float64)
	for _, v := range estimatedEarningsData {
		m[v.Site] = v.TotalEarnings
	}
	return m, nil
}

func (uc *AdsenseUsecase) GetSiteEstimatedEarnings(ctx context.Context, s, e time.Time) ([]*AdsenseSiteAdFormatData, error) {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)

	estimatedEarningsData, err := uc.siteAdFormatRepo.GetSiteEstimatedEarnings(ctx, time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, cstSh),
		time.Date(e.Year(), e.Month(), e.Day(), 0, 0, 0, 0, cstSh))
	if err != nil {
		return nil, err
	}
	return estimatedEarningsData, nil
}

// generateRowData 生成单行Excel数据
func (uc *AdsenseUsecase) generateRowData(item *AdsenseSiteAdFormatData, gaDataMap map[string]*AnalyticsSiteDataResponse, gaVisitPageCountMap map[string]int,
	maxEarningsMap map[string]float64) []interface{} {
	row := make([]interface{}, 15)
	row[0] = item.CooperationChannel
	row[1] = item.Site
	row[2] = item.EstimatedEarnings

	// 处理历史收入和波动
	if maxEarnings, ok := maxEarningsMap[item.Site]; ok {
		row[3] = maxEarnings
		percentage := (item.EstimatedEarnings - maxEarnings) / maxEarnings * 100
		row[4] = math.Round(percentage*100) / 100
		if math.IsNaN(percentage) {
			row[4] = 0.0
		}
	} else {
		row[3] = 0.0
		row[4] = 0.0
	}
	row[9] = gaVisitPageCountMap[item.Site]

	// 处理GA数据
	if gaData, ok := gaDataMap[item.Site]; ok {
		row[5] = math.Round(gaData.AverageSessionDuration*100) / 100
		row[6] = math.Round(gaData.AverageEngagementTimePerActiveUser*100) / 100
		row[7] = math.Round(gaData.ScreenPageViewsPerUser*100) / 100
		row[8] = gaData.PageViews

		// 处理浏览器数据
		sumBrowserActiveUsers := gaData.SumBrowserActiveUsers
		if sumBrowserActiveUsers == 0 {
			row[10] = "0(0.0000)"
			row[11] = "0(0.0000)"
			row[12] = "0(0.0000)"
			row[13] = "0(0.0000)"
			row[14] = "0(0.0000)"
		} else {
			row[10] = fmt.Sprintf("%d(%.4f)", gaData.BrowserMap[BrowserAndroidWebview], float64(gaData.BrowserMap[BrowserAndroidWebview])/float64(sumBrowserActiveUsers))
			row[11] = fmt.Sprintf("%d(%.4f)", gaData.BrowserMap[BrowserSafari], float64(gaData.BrowserMap[BrowserSafari])/float64(sumBrowserActiveUsers))
			row[12] = fmt.Sprintf("%d(%.4f)", gaData.BrowserMap[BrowserChrome], float64(gaData.BrowserMap[BrowserChrome])/float64(sumBrowserActiveUsers))
			row[13] = fmt.Sprintf("%d(%.4f)", gaData.BrowserMap[BrowserEdge], float64(gaData.BrowserMap[BrowserEdge])/float64(sumBrowserActiveUsers))
			row[14] = fmt.Sprintf("%d(%.4f)", gaData.BrowserMap[BrowserSafariInApp], float64(gaData.BrowserMap[BrowserSafariInApp])/float64(sumBrowserActiveUsers))
		}
	} else {
		row[5] = 0.0
		row[6] = 0.0
		row[7] = 0.0
		row[8] = 0
		row[9] = 0
		row[10] = "0(0.0000)"
		row[11] = "0(0.0000)"
		row[12] = "0(0.0000)"
		row[13] = "0(0.0000)"
		row[14] = "0(0.0000)"
	}

	return row
}

// writeDataToSheet 写入数据到指定工作表
func (uc *AdsenseUsecase) writeDataToSheet(f *excelize.File, sheetName string, data []*AdsenseSiteAdFormatData,
	gaDataMap map[string]*AnalyticsSiteDataResponse, gaVisitPageCountMap map[string]int, maxEarningsMap map[string]float64, filterMinEarnings bool) error {
	sw, err := f.NewStreamWriter(sheetName)
	if err != nil {
		return err
	}

	// 设置表头
	header := []interface{}{"所属渠道", "网站", "累计收入", "历史收入", "区间收入波动", "每用户平均会话时长", "每用户平均互动时长", "每用户浏览次数", "总浏览次数", "总页面量", "Android Webview", "Safari", "Chrome", "Edge", "Safari(in-app)"}
	if err := sw.SetRow("A1", header); err != nil {
		return err
	}

	// 写入数据行
	realRowIndex := 0
	for rowID := 0; rowID < len(data); rowID++ {
		// 根据过滤条件决定是否跳过
		if filterMinEarnings && data[rowID].EstimatedEarnings < uc.minEstimatedEarnings {
			continue
		}

		row := uc.generateRowData(data[rowID], gaDataMap, gaVisitPageCountMap, maxEarningsMap)

		// 计算实际行号
		var cellName string
		if filterMinEarnings {
			cellName, err = excelize.CoordinatesToCellName(1, realRowIndex+2)
			realRowIndex++
		} else {
			cellName, err = excelize.CoordinatesToCellName(1, rowID+2)
		}
		if err != nil {
			return err
		}

		if err := sw.SetRow(cellName, row); err != nil {
			return err
		}
	}

	return sw.Flush()
}

func (uc *AdsenseUsecase) GenerateGaChannelReportExcel(data []*AdsenseSiteAdFormatData, gaDataMap map[string]*AnalyticsSiteDataResponse, gaVisitPageCountMap map[string]int, maxEarningsMap map[string]float64) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			return
		}
	}()

	// 创建工作表
	_, err := f.NewSheet(ExcelSheetReport)
	if err != nil {
		return nil, err
	}
	_, err = f.NewSheet(ExcelSheetReport2)
	if err != nil {
		return nil, err
	}
	err = f.DeleteSheet(ExcelSheetDefault)
	if err != nil {
		return nil, err
	}

	// 写入第一个工作表（过滤收入小于最小值的数据）
	if err := uc.writeDataToSheet(f, ExcelSheetReport, data, gaDataMap, gaVisitPageCountMap, maxEarningsMap, true); err != nil {
		return nil, err
	}

	// 写入第二个工作表（包含所有数据）
	if err := uc.writeDataToSheet(f, ExcelSheetReport2, data, gaDataMap, gaVisitPageCountMap, maxEarningsMap, false); err != nil {
		return nil, err
	}

	buffer, err := f.WriteToBuffer()
	if err != nil {
		return nil, err
	}
	return buffer, nil
}

func (uc *AdsenseUsecase) SendGaChannelReportEmail(ctx context.Context, data *bytes.Buffer, sendInfo *SendInfo) error {
	err := uc.email.SendChannelGaEmail(uc.channelGaReportTo, sendInfo.ReportName, data)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) GetSiteGaData(ctx context.Context, start time.Time, end time.Time) (map[string]*AnalyticsSiteDataResponse, map[string]int, error) {
	data, err := uc.analyticsSiteRepo.GetSiteGaData(ctx, start, end)
	if err != nil {
		return nil, nil, err
	}
	siteDataMap := make(map[string]*AnalyticsSiteDataResponse)
	for _, analyticsSiteData := range data {
		// Android Webview  Safari (in-app) Chrome  Edge Safari 只保留这几个浏览器的数据，其它浏览器的数据合并到其它，并计算百分比
		browserMap := make(map[string]int)
		sumBrowserActiveUsers := 0
		for browserName, activeUsers := range analyticsSiteData.BrowserMap {
			if browserName == BrowserAndroidWebview || browserName == BrowserSafariInApp || browserName == BrowserChrome || browserName == BrowserEdge || browserName == BrowserSafari {
				browserMap[browserName] = activeUsers
				sumBrowserActiveUsers += activeUsers
			} else {
				if _, ok := browserMap[BrowserOther]; !ok {
					browserMap[BrowserOther] = 0
				}
				browserMap[BrowserOther] += activeUsers
				sumBrowserActiveUsers += activeUsers
			}
		}

		analyticsSiteData.BrowserMap = browserMap
		analyticsSiteData.SumBrowserActiveUsers = sumBrowserActiveUsers
		siteDataMap[analyticsSiteData.Site] = analyticsSiteData
	}
	visitPageCountMap, err := uc.analyticsSiteRepo.GetVisitPageCountGaData(ctx, start, end)
	if err != nil {
		return nil, nil, err
	}
	return siteDataMap, visitPageCountMap, nil
}

func (uc *AdsenseUsecase) SaveSiteAdUnitData(ctx context.Context, data *Adsense, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsenseSiteAdUnitData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsenseSiteAdUnitData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			Site:                  row[0],
			AdUnit:                row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
			ActiveViewTime:        cast.StringToInt(row[15]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	err := uc.siteAdUnitRepo.Create(ctx, datas)
	if err != nil {
		return err
	}

	return nil
}

func (uc *AdsenseUsecase) SavePageURLCountryAdFormatData(ctx context.Context, data *Adsense, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsensePageURLCountryAdFormatData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsensePageURLCountryAdFormatData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			PageURL:               row[0],
			AdFormat:              row[2],
			Country:               row[3],
			EstimatedEarnings:     cast.StringToFloat(row[4]),
			PageViews:             cast.StringToInt(row[5]),
			PageViewsRpm:          cast.StringToFloat(row[6]),
			IMPRESSIONS:           cast.StringToInt(row[7]),
			ImpressionsRpm:        cast.StringToFloat(row[8]),
			AdRequestsCoverage:    cast.StringToFloat(row[9]),
			CLICKS:                cast.StringToInt(row[10]),
			AdRequests:            cast.StringToInt(row[11]),
			ImpressionsCtr:        cast.StringToFloat(row[12]),
			ActiveViewViewability: cast.StringToFloat(row[13]),
			CostPerClick:          cast.StringToFloat(row[14]),
			MatchedAdRequests:     cast.StringToInt(row[15]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	err := uc.pageURLCountryAdFormatRepo.Create(ctx, datas)
	if err != nil {
		return err
	}
	return nil
}

func (uc *AdsenseUsecase) SavePageURLCountryData(ctx context.Context, data *Adsense, pubID string) error {
	cstSh, _ := time.LoadLocation(TimeZoneShanghai)
	datas := make([]*AdsensePageURLCountryData, 0)
	for _, row := range data.Data {
		date, err := time.Parse(time.DateOnly, row[1])
		if err != nil {
			log.Error(ErrMsgDateParsing, err)
			return err
		}
		ascd := &AdsensePageURLCountryData{
			Date:                  time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, cstSh),
			PageURL:               row[0],
			Country:               row[2],
			EstimatedEarnings:     cast.StringToFloat(row[3]),
			PageViews:             cast.StringToInt(row[4]),
			PageViewsRpm:          cast.StringToFloat(row[5]),
			IMPRESSIONS:           cast.StringToInt(row[6]),
			ImpressionsRpm:        cast.StringToFloat(row[7]),
			AdRequestsCoverage:    cast.StringToFloat(row[8]),
			CLICKS:                cast.StringToInt(row[9]),
			AdRequests:            cast.StringToInt(row[10]),
			ImpressionsCtr:        cast.StringToFloat(row[11]),
			ActiveViewViewability: cast.StringToFloat(row[12]),
			CostPerClick:          cast.StringToFloat(row[13]),
			MatchedAdRequests:     cast.StringToInt(row[14]),
			Account:               pubID,
			Platform:              PlatformAdsense,
		}
		datas = append(datas, ascd)

	}
	err := uc.pageURLCountryRepo.Create(ctx, datas)
	if err != nil {
		return err
	}
	return nil
}

func getSiteDifferences(adsenseSiteData map[string]*AdsenseSiteData, adManagerSiteData map[string]*AdManagerSite) ([]string, []string) {
	// Array for keys that exist in both maps
	commonKeys := make([]string, 0)
	// Array for keys that exist in only one map
	uniqueKeys := make([]string, 0)

	// Check all keys from adsenseSiteData
	for key := range adsenseSiteData {
		if _, exists := adManagerSiteData[key]; exists {
			commonKeys = append(commonKeys, key)
		} else {
			uniqueKeys = append(uniqueKeys, key)
		}
	}

	// Check keys from adManagerSiteData that might not be in adsenseSiteData
	for key := range adManagerSiteData {
		if _, exists := adsenseSiteData[key]; !exists {
			uniqueKeys = append(uniqueKeys, key)
		}
	}

	return commonKeys, uniqueKeys
}
