package data

import (
	"context"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channel"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelhkd"
)

type channelRepo struct {
	data *Data
	log  *log.Helper
}

func (r *channelRepo) ListHKD(ctx context.Context, start time.Time, end time.Time) ([]*biz.AdsenseChannelData, error) {
	all, err := r.data.db.ChannelHKD.Query().Where(channelhkd.DateLTE(end), channelhkd.DateGTE(start)).All(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdsenseChannelData, 0)
	for _, data := range all {
		res = append(res, &biz.AdsenseChannelData{
			Date:                  data.Date,
			Channel:               data.Channel,
			ChannelID:             data.ChannelID,
			EstimatedEarnings:     data.EstimatedEarnings,
			PageViews:             data.PageViews,
			PageViewsRpm:          data.PageViewsRpm,
			IMPRESSIONS:           data.Impressions,
			ImpressionsRpm:        data.ImpressionsRpm,
			AdRequestsCoverage:    data.AdRequestsCoverage,
			CLICKS:                data.Clicks,
			AdRequests:            data.AdRequests,
			ImpressionsCtr:        data.ImpressionsCtr,
			ActiveViewViewability: data.ActiveViewViewability,
			CostPerClick:          data.CostPerClick,
			MatchedAdRequests:     data.MatchedAdRequests,
		})
	}
	return res, nil
}

func (r *channelRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseChannelData) error {
	bulk := make([]*ent.ChannelHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.ChannelHKD.Create().SetDate(data.Date).SetChannel(data.Channel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.ChannelHKD.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "account", "platform")).UpdateNewValues().Exec(ctx)
			// _, err := tx.Channel.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func (r *channelRepo) List(ctx context.Context, start, end time.Time) ([]*biz.AdsenseChannelData, error) {
	all, err := r.data.db.Channel.Query().Where(channel.DateLTE(end), channel.DateGTE(start)).All(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdsenseChannelData, 0)
	for _, data := range all {
		res = append(res, &biz.AdsenseChannelData{
			Date:                  data.Date,
			Channel:               data.Channel,
			ChannelID:             data.ChannelID,
			EstimatedEarnings:     data.EstimatedEarnings,
			PageViews:             data.PageViews,
			PageViewsRpm:          data.PageViewsRpm,
			IMPRESSIONS:           data.Impressions,
			ImpressionsRpm:        data.ImpressionsRpm,
			AdRequestsCoverage:    data.AdRequestsCoverage,
			CLICKS:                data.Clicks,
			AdRequests:            data.AdRequests,
			ImpressionsCtr:        data.ImpressionsCtr,
			ActiveViewViewability: data.ActiveViewViewability,
			CostPerClick:          data.CostPerClick,
			MatchedAdRequests:     data.MatchedAdRequests,
			Account:               data.Account,
			Platform:              data.Platform,
		})
	}
	return res, nil
}

func (r *channelRepo) Create(ctx context.Context, datas []*biz.AdsenseChannelData) error {
	bulk := make([]*ent.ChannelCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.Channel.Create().SetDate(data.Date).SetChannel(data.Channel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetChannelID(data.ChannelID).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.Channel.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "channel", "channel_id", "account", "platform")).UpdateNewValues().Exec(ctx)
			// _, err := tx.Channel.createBulk(bulk...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func NewChannelRepo(data *Data, logger log.Logger) biz.AdSenseChannelRepo {
	return &channelRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/channel")),
	}
}
