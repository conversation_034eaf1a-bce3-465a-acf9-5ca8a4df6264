package data

import (
	"context"
	"strconv"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/urlutil"
)

type urlChannelCountryAdFormatRepo struct {
	data *Data
	log  *log.Helper
}

func (r *urlChannelCountryAdFormatRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseURLChannelCountryAdFormatData) error {
	bulkInfo := make([]*ent.UrlChannelCountryAdFormatInfoPartitionHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {

		infos := urlutil.FormatURL("https://" + data.UrlChannel)
		isRoot, _ := strconv.ParseBool(infos[5])
		bulkInfo = append(bulkInfo, tx.UrlChannelCountryAdFormatInfoPartitionHKD.Create().SetDate(data.Date).SetURLChannel(data.UrlChannel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetCountry(data.Country).SetAdFormat(data.AdFormat).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).SetIsRoot(isRoot).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err = tx.UrlChannelCountryAdFormatInfoPartitionHKD.CreateBulk(bulkInfo...).OnConflict(sql.ConflictColumns("date", "url_channel", "ad_format", "country", "account", "platform")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulkInfo = bulkInfo[:0]
		}
	}
	return tx.Commit()
}

func (r *urlChannelCountryAdFormatRepo) Create(ctx context.Context, datas []*biz.AdsenseURLChannelCountryAdFormatData) error {
	const batchSize = 1800

	for i := 0; i < len(datas); i += batchSize {
		end := i + batchSize
		if end > len(datas) {
			end = len(datas)
		}

		if err := r.processBatch(ctx, datas[i:end]); err != nil {
			return err
		}
	}
	return nil
}

func (r *urlChannelCountryAdFormatRepo) processBatch(ctx context.Context, batch []*biz.AdsenseURLChannelCountryAdFormatData) error {
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}

	//bulk := make([]*ent.UrlChannelCountryAdFormatCreate, 0, len(batch))
	bulkInfo := make([]*ent.UrlChannelCountryAdFormatInfoPartitionCreate, 0, len(batch))

	for _, data := range batch {
		//bulk = append(bulk, tx.UrlChannelCountryAdFormat.Create().
		//	SetDate(data.Date).
		//	SetURLChannel(data.UrlChannel).
		//	SetEstimatedEarnings(data.EstimatedEarnings).
		//	SetPageViews(data.PageViews).
		//	SetPageViewsRpm(data.PageViewsRpm).
		//	SetCountry(data.Country).
		//	SetAdFormat(data.AdFormat).
		//	SetImpressions(data.IMPRESSIONS).
		//	SetImpressionsRpm(data.ImpressionsRpm).
		//	SetAdRequestsCoverage(data.AdRequestsCoverage).
		//	SetClicks(data.CLICKS).
		//	SetAdRequests(data.AdRequests).
		//	SetImpressionsCtr(data.ImpressionsCtr).
		//	SetActiveViewViewability(data.ActiveViewViewability).
		//	SetMatchedAdRequests(data.MatchedAdRequests).
		//	SetCostPerClick(data.CostPerClick))

		infos := urlutil.FormatURL("https://" + data.UrlChannel)
		isRoot, _ := strconv.ParseBool(infos[5])

		bulkInfo = append(bulkInfo, tx.UrlChannelCountryAdFormatInfoPartition.Create().
			SetDate(data.Date).
			SetURLChannel(data.UrlChannel).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).
			SetPageViewsRpm(data.PageViewsRpm).
			SetCountry(data.Country).
			SetAdFormat(data.AdFormat).
			SetImpressions(data.IMPRESSIONS).
			SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).
			SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).
			SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).
			SetSubChannel(infos[2]).
			SetPageType(infos[3]).
			SetAppID(infos[4]).
			SetIsRoot(isRoot).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
	}

	//err = tx.UrlChannelCountryAdFormat.CreateBulk(bulk...).
	//	OnConflict(sql.ConflictColumns("date", "url_channel", "ad_format", "country")).
	//	UpdateNewValues().
	//	Exec(ctx)
	//if err != nil {
	//	return rollback(tx, err)
	//}

	err = tx.UrlChannelCountryAdFormatInfoPartition.CreateBulk(bulkInfo...).
		OnConflict(sql.ConflictColumns("date", "url_channel", "ad_format", "country", "account", "platform")).
		UpdateNewValues().
		Exec(ctx)
	if err != nil {
		return rollback(tx, err)
	}

	return tx.Commit()
}
func NewUrlChannelCountryAdFormatRepo(data *Data, logger log.Logger) biz.AdSenseURLChannelCountryAdFormatRepo {
	return &urlChannelCountryAdFormatRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/urlChannel/country/adformat")),
	}
}
