package data

import (
	"context"
	"strconv"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/urlutil"
)

type urlChannelRepo struct {
	data *Data
	log  *log.Helper
}

func (r *urlChannelRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseURLChannelData) error {
	bulkInfo := make([]*ent.UrlChannelAllInfoHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {

		infos := urlutil.FormatURL("https://" + data.UrlChannel)
		isRoot, _ := strconv.ParseBool(infos[5])
		bulkInfo = append(bulkInfo, tx.UrlChannelAllInfoHKD.Create().
			SetDate(data.Date).SetURLChannel(data.UrlChannel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetCostPerClick(data.CostPerClick).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).SetIsRoot(isRoot).
			SetAccount(data.Account).SetPlatform(data.Platform))

		if (i+1)%3000 == 0 || i == len(datas)-1 {
			err = tx.UrlChannelAllInfoHKD.CreateBulk(bulkInfo...).OnConflict(sql.ConflictColumns("date", "url_channel", "account", "platform")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulkInfo = bulkInfo[:0]
		}
	}
	return tx.Commit()
}

func (r *urlChannelRepo) Create(ctx context.Context, datas []*biz.AdsenseURLChannelData) error {
	//bulk := make([]*ent.UrlChannelAllCreate, 0)
	bulkInfo := make([]*ent.UrlChannelAllInfoCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		//bulk = append(bulk, tx.UrlChannelAll.Create().SetDate(data.Date).SetURLChannel(data.UrlChannel).SetEstimatedEarnings(data.EstimatedEarnings).
		//	SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
		//	SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
		//	SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
		//	SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
		//	SetActiveViewViewability(data.ActiveViewViewability).
		//	SetMatchedAdRequests(data.MatchedAdRequests).
		//	SetCostPerClick(data.CostPerClick))
		infos := urlutil.FormatURL("https://" + data.UrlChannel)
		isRoot, _ := strconv.ParseBool(infos[5])
		bulkInfo = append(bulkInfo, tx.UrlChannelAllInfo.Create().
			SetDate(data.Date).SetURLChannel(data.UrlChannel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetCostPerClick(data.CostPerClick).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).SetIsRoot(isRoot).
			SetAccount(data.Account).SetPlatform(data.Platform))

		if (i+1)%3000 == 0 || i == len(datas)-1 {
			//err := tx.UrlChannelAll.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "url_channel")).UpdateNewValues().Exec(ctx)
			//
			//// _, err := tx.UrlChannelAll.createBulk(bulk...).Save(ctx)
			//if err != nil {
			//	return rollback(tx, err)
			//}
			err = tx.UrlChannelAllInfo.CreateBulk(bulkInfo...).OnConflict(sql.ConflictColumns("date", "url_channel", "account", "platform")).UpdateNewValues().Exec(ctx)

			// _, err = tx.UrlChannelAllInfo.createBulk(bulkInfo...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			//bulk = bulk[:0]
			bulkInfo = bulkInfo[:0]
		}
	}
	return tx.Commit()
}

func NewUrlChannelRepo(data *Data, logger log.Logger) biz.AdSenseURLChannelRepo {
	return &urlChannelRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/urlChannel")),
	}
}
