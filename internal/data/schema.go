/**
* <AUTHOR>
* @Date 2024/2/6
**/

package data

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountryadformathkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountryadformatpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/gamecountryhkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/gamecountrypartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/siteadformathistory"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitebusiness"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitecountryadformathkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitecountryadformatpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/urlchannelcountryadformatinfopartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/urlchannelcountryadformatinfopartitionhkd"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/partition"
)

type shardingTablesRepo struct {
	data *Data
	log  *log.Helper
}

const siteAdFormatHistoryIndexSql = `
CREATE UNIQUE INDEX idx_unique_site_adformat_history
ON site_ad_format_histories(site, date, ad_format, collected_at, account, platform);
CREATE INDEX idx_date_collected ON site_ad_format_histories(date, collected_at);
CREATE INDEX idx_account ON site_ad_format_histories(account);
CREATE INDEX idx_platform ON site_ad_format_histories(platform);
ALTER TABLE site_ad_format_histories ADD CONSTRAINT site_ad_format_histories__pkey PRIMARY KEY ("id","date", "collected_at");

	`

const siteBusinessIndexSql = `
CREATE UNIQUE INDEX idx_unique_site_business
ON site_businesses(site, date);
CREATE INDEX idx_site_businesses_site ON site_businesses(site);
CREATE INDEX idx_site_businesses_date ON site_businesses(date);
ALTER TABLE site_businesses ADD CONSTRAINT site_business_pkey PRIMARY KEY ("site","date");
`

const urlChannelCountryAdFormatInfoIndexSql = `CREATE UNIQUE INDEX "%s__a"
ON "public"."%s" USING btree ( 		"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,"url_channel" COLLATE "pg_catalog"."default" ,"ad_format" COLLATE "pg_catalog"."default" ,"country" COLLATE "pg_catalog"."default" ,"account" COLLATE "pg_catalog"."default" ,"platform" COLLATE "pg_catalog"."default" )
;
CREATE INDEX "%s_ad_format" 
ON "public"."%s" USING btree ( "ad_format" COLLATE "pg_catalog"."default" ) 
;
CREATE INDEX "%s_app_id" 
ON "public"."%s" USING btree ( "app_id" COLLATE "pg_catalog"."default" ) 
;
CREATE INDEX "%s_country" 
ON "public"."%s" USING btree ( "country" COLLATE "pg_catalog"."default" ) 
;
CREATE INDEX "%s_date" 
ON "public"."%s" USING btree ( "date" ) 
;
CREATE INDEX "%s_is_root" 
ON "public"."%s" USING btree ( "is_root" ) 
;
CREATE INDEX "%s_page_type" 
ON "public"."%s" USING btree ( "page_type" COLLATE "pg_catalog"."default" ) 
;
CREATE INDEX "%s_site" 
ON "public"."%s" USING btree ( "site" COLLATE "pg_catalog"."default" ) 
;
CREATE INDEX "%s_site_url_channel" 
ON "public"."%s" USING btree ( "site" COLLATE "pg_catalog"."default","url_channel" COLLATE "pg_catalog"."default"  ) 
;
CREATE INDEX "%s_sub_channel" 
ON "public"."%s" USING btree ( "sub_channel" COLLATE "pg_catalog"."default" ) 
;
CREATE INDEX "%s_url_channel"
ON "public"."%s" USING btree ( "url_channel" COLLATE "pg_catalog"."default" )
;
CREATE INDEX "%s_account"
ON "public"."%s" USING btree ( "account" COLLATE "pg_catalog"."default" )
;
CREATE INDEX "%s_platform"
ON "public"."%s" USING btree ( "platform" COLLATE "pg_catalog"."default" )
;
ALTER TABLE "public"."%s" ADD CONSTRAINT "%s__pkey" PRIMARY KEY ("id","date");

		`

const siteCountryAdFormatIndexSql = `
CREATE UNIQUE INDEX "%s_date_site_ad_format_country"
ON "public"."%s" USING btree ( 		"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,"site" COLLATE "pg_catalog"."default" ,"ad_format" COLLATE "pg_catalog"."default" ,"country" COLLATE "pg_catalog"."default" ,"account" COLLATE "pg_catalog"."default" ,"platform" COLLATE "pg_catalog"."default" );

CREATE INDEX "%s_date" 
ON "public"."%s" USING btree ( "date" ) 
;
CREATE INDEX "%s_site" 
ON "public"."%s" USING btree ( "site" COLLATE "pg_catalog"."default" ) 
;
CREATE INDEX "%s_country" 
ON "public"."%s" USING btree ( "country" COLLATE "pg_catalog"."default" ) 
;
CREATE INDEX "%s_ad_format" 
ON "public"."%s" USING btree ( "ad_format" COLLATE "pg_catalog"."default" ) 
;
CREATE INDEX "%s_date_site" 
ON "public"."%s" USING btree ( "date","site" COLLATE "pg_catalog"."default"  ) 
;
CREATE INDEX "%s_date_country" 
ON "public"."%s" USING btree ( "date","country" COLLATE "pg_catalog"."default"  ) 
;
CREATE INDEX "%s_date_site_country" 
ON "public"."%s" USING btree ( "date","site" COLLATE "pg_catalog"."default" ,"country" COLLATE "pg_catalog"."default"  ) 
;
CREATE INDEX "%s_date_ad_format"
ON "public"."%s" USING btree ( "date","ad_format" COLLATE "pg_catalog"."default"  )
;
CREATE INDEX "%s_account"
ON "public"."%s" USING btree ( "account" COLLATE "pg_catalog"."default" )
;
CREATE INDEX "%s_platform"
ON "public"."%s" USING btree ( "platform" COLLATE "pg_catalog"."default" )
;
ALTER TABLE "public"."%s" ADD CONSTRAINT "%s__pkey" PRIMARY KEY ("id","date");
	`

func (r *shardingTablesRepo) tableExists(tableName string) (bool, error) {
	var exists bool
	err := r.data.stddb.QueryRow("SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = $1)", tableName).Scan(&exists)
	if err != nil {
		return false, err
	}
	return exists, nil
}

func (r *shardingTablesRepo) CreatePartitionTables(_ context.Context) (err error) {
	// 查询数据表是否存在
	err = r.createChannelCountryAdFormatPartitionTable()
	if err != nil {
		return
	}
	err = r.createChannelCountryAdFormatHKDPartitionTable()
	if err != nil {
		return
	}
	err = r.createSiteCountryAdFormatPartitionTable()
	if err != nil {
		return err
	}
	err = r.createSiteAdFormatHistoryPartitionTable()
	if err != nil {
		return err
	}
	err = r.createSiteBusinessPartitionTable()
	if err != nil {
		return err
	}
	err = r.createSiteCountryAdFormatHKDPartitionTable()
	if err != nil {
		return err
	}
	err = r.createUrlChannelCountryAdFormatInfoPartitionTable()
	if err != nil {
		return err
	}
	err = r.createUrlChannelCountryAdFormatInfoHKDPartitionTable()
	if err != nil {
		return err
	}
	err = r.createGameCountryPartitionTable()
	if err != nil {
		return err
	}
	err = r.createGameCountryHKDPartitionTable()
	if err != nil {
		return err
	}
	return
}

func (r *shardingTablesRepo) createSiteCountryAdFormatPartitionTable() error {
	tableName := sitecountryadformatpartition.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."site_country_ad_format_partitions" (
		 id                      bigint generated by default as identity,
    date                    timestamp with time zone not null,
    site                    varchar                  not null,
    country                 varchar                  not null,
    ad_format               varchar                  not null,
    estimated_earnings      double precision         not null,
    page_views_rpm          double precision         not null,
    page_views              bigint                   not null,
    impressions             bigint                   not null,
    impressions_rpm         double precision         not null,
    ad_requests_coverage    double precision         not null,
    clicks                  bigint                   not null,
    ad_requests             bigint                   not null,
    impressions_ctr         double precision         not null,
    active_view_viewability double precision         not null,
    matched_ad_requests     bigint default 0,
    cost_per_click          double precision         not null,
    account                 text   default 'pub-****************'::text not null,
    platform                text   default 'Adsense'::text              not null
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	sqlIndex := fmt.Sprintf(siteCountryAdFormatIndexSql, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName,
		tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
	_, err = r.data.stddb.Exec(sqlIndex)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) CreateChildTables(_ context.Context, year int) (err error) {
	err = r.createChannelCountryAdFormatPartitionChildTables(year)
	if err != nil {
		return err
	}
	err = r.createChannelCountryAdFormatHKDPartitionChildTables(year)
	if err != nil {
		return err
	}
	err = r.createSiteCountryAdFormatPartitionChildTables(year)
	if err != nil {
		return err
	}
	err = r.createSiteCountryAdFormatHKDPartitionChildTables(year)
	if err != nil {
		return err
	}
	err = r.createUrlChannelCountryAdFormatPartitionChildTables(year)
	if err != nil {
		return err
	}
	err = r.createUrlChannelCountryAdFormatHKDPartitionChildTables(year)
	if err != nil {
		return err
	}
	err = r.createGameCountryPartitionChildTables(year)
	if err != nil {
		return err
	}
	err = r.createGameCountryHKDPartitionChildTables(year)
	if err != nil {
		return err
	}
	err = r.createSiteAdFormatHistoryChildTables(year)
	if err != nil {
		return err
	}
	err = r.createSiteBusinessChildTables(year)
	if err != nil {
		return err
	}
	return
}

func (r *shardingTablesRepo) createUrlChannelCountryAdFormatPartitionChildTables(year int) error {
	yearTables := getYearlyTables(urlchannelcountryadformatinfopartition.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {

		// 查询数据表是否存在
		exist := false
		var err error
		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, urlchannelcountryadformatinfopartition.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}
		//tableName := monthTable

		//sqlIndex := fmt.Sprintf(urlChannelCountryAdFormatInfoChildTableIndexSql, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
		//
		//_, err = r.data.stddb.Exec(sqlIndex)
		//if err != nil {
		//	return err
		//}
		monthPo++
	}
	return nil
}

func (r *shardingTablesRepo) createSiteCountryAdFormatPartitionChildTables(year int) error {
	yearTables := getYearlyTables(sitecountryadformatpartition.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {

		// 查询数据表是否存在
		exist := false
		var err error

		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, sitecountryadformatpartition.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}
		//tableName := monthTable
		//sqlIndex := fmt.Sprintf(siteCountryAdFormatChildIndexSql, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
		//
		//_, err = r.data.stddb.Exec(sqlIndex)
		//if err != nil {
		//	return err
		//}
		monthPo++
	}
	return nil
}

func (r *shardingTablesRepo) createChannelCountryAdFormatPartitionChildTables(year int) error {
	yearTables := getYearlyTables(channelcountryadformatpartition.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {
		var err error
		// 查询数据表是否存在
		exist := false
		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, channelcountryadformatpartition.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}
		//tableName := monthTable
		//sqlIndex := fmt.Sprintf(`CREATE INDEX "%s_ad_format" ON "public"."%s" USING btree (
		//	"ad_format" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		//	);
		//	CREATE INDEX "%s_channel" ON "public"."%s" USING btree (
		//	"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		//	);
		//	CREATE INDEX "%s_channel_id" ON "public"."%s" USING btree (
		//	"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		//	);
		//	CREATE INDEX "%s_country" ON "public"."%s" USING btree (
		//	"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		//	);
		//	CREATE INDEX "%s_date" ON "public"."%s" USING btree (
		//	"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
		//	);
		//	CREATE UNIQUE INDEX "%s_date_channel_channe_id_country_ad_format" ON "public"."%s" USING btree (
		//	"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
		//	"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"ad_format" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST);
		//`, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
		//
		////	ALTER TABLE "public"."%s" ADD CONSTRAINT "%s_pkey" PRIMARY KEY ("id");
		////ALTER TABLE "public"."%s" ADD CONSTRAINT "%s_pkey" PRIMARY KEY ("id","date");
		//
		//_, err = r.data.stddb.Exec(sqlIndex)
		//if err != nil {
		//	return err
		//}
		monthPo++
	}
	return nil
}

func (r *shardingTablesRepo) createUrlChannelCountryAdFormatInfoPartitionTable() error {
	tableName := urlchannelcountryadformatinfopartition.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."url_channel_country_ad_format_info_partitions" (
		"id"  bigint generated by default as identity ,
		 "date" timestamp with time zone NOT NULL ,
		 "url_channel" varchar NOT NULL ,
		 "ad_format" varchar NOT NULL ,
		 "country" varchar NOT NULL ,
		 "estimated_earnings" double precision NOT NULL ,
		 "page_views_rpm" double precision NOT NULL ,
		 "page_views" bigint NOT NULL ,
		 "impressions" bigint NOT NULL ,
		 "impressions_rpm" double precision NOT NULL ,
		 "ad_requests_coverage" double precision NOT NULL ,
		 "clicks" bigint NOT NULL ,
		 "ad_requests" bigint NOT NULL ,
		 "impressions_ctr" double precision NOT NULL ,
		 "active_view_viewability" double precision NOT NULL ,
		 "cost_per_click" double precision NOT NULL ,
    	 account   text default 'pub-****************'::text not null,
    	 platform  text default 'Adsense'::text              not null,
		 "matched_ad_requests" bigint NOT NULL ,
		 "site" varchar NOT NULL ,
		 "is_root" boolean NOT NULL ,
		 "sub_channel" varchar NOT NULL ,
		 "page_type" varchar NOT NULL ,
		 "app_id" varchar NOT NULL 
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	sqlIndex := fmt.Sprintf(urlChannelCountryAdFormatInfoIndexSql, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName,
		tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
	_, err = r.data.stddb.Exec(sqlIndex)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) createChannelCountryAdFormatPartitionTable() error {
	tableName := channelcountryadformatpartition.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."channel_country_ad_format_partitions" (
		"id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
		INCREMENT 1
	MINVALUE  1
	MAXVALUE 9223372036854775807
	START 1
	),
	"date" timestamptz(6) NOT NULL,
		"channel" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"channel_id" varchar COLLATE "pg_catalog"."default",
		"country" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"ad_format" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"estimated_earnings" float8 NOT NULL,
		"page_views_rpm" float8 NOT NULL,
		"page_views" int8 NOT NULL,
		"impressions" int8 NOT NULL,
		"impressions_rpm" float8 NOT NULL,
		"ad_requests_coverage" float8 NOT NULL,
		"clicks" int8 NOT NULL,
		"ad_requests" int8 NOT NULL,
		"impressions_ctr" float8 NOT NULL,
		"active_view_viewability" float8 NOT NULL,
		"cost_per_click" float8 NOT NULL,
		"matched_ad_requests" int8 DEFAULT 0,
        account                 text default 'pub-****************'::text not null,
        platform                text default 'Adsense'::text              not null
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	sqlIndex := fmt.Sprintf(`
CREATE INDEX "%s_ad_format" ON "public"."%s" USING btree (
			"ad_format" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
			);
			CREATE INDEX "%s_channel" ON "public"."%s" USING btree (
			"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
			);
			CREATE INDEX "%s_channel_id" ON "public"."%s" USING btree (
			"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
			);
			CREATE INDEX "%s_country" ON "public"."%s" USING btree (
			"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
			);
			CREATE INDEX "%s_date" ON "public"."%s" USING btree (
			"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
			);
		CREATE UNIQUE INDEX "%s_date_channel_channe_id_country_ad_format" ON "public"."%s" USING btree (
		"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
		"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"ad_format" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_account" ON "public"."%s" USING btree (
		"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_platform" ON "public"."%s" USING btree (
		"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
ALTER TABLE "public"."%s" ADD CONSTRAINT "%s__pkey" PRIMARY KEY ("id","date");
	`, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
	_, err = r.data.stddb.Exec(sqlIndex)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) createGameCountryPartitionTable() error {
	tableName := gamecountrypartition.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."game_country_partitions" (
		"id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
		INCREMENT 1
	MINVALUE  1
	MAXVALUE 9223372036854775807
	START 1
	),
	"date" timestamptz(6) NOT NULL,
		"channel" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"channel_id" varchar COLLATE "pg_catalog"."default",
		"country" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"estimated_earnings" float8 NOT NULL,
		"page_views_rpm" float8 NOT NULL,
		"page_views" int8 NOT NULL,
		"impressions" int8 NOT NULL,
		"impressions_rpm" float8 NOT NULL,
		"ad_requests_coverage" float8 NOT NULL,
		"clicks" int8 NOT NULL,
		"ad_requests" int8 NOT NULL,
		"impressions_ctr" float8 NOT NULL,
		"active_view_viewability" float8 NOT NULL,
		"cost_per_click" float8 NOT NULL,
		"matched_ad_requests" int8 NOT NULL,
		  account                 text default 'pub-****************'::text not null,
         platform                text default 'Adsense'::text              not null
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	sqlIndex := fmt.Sprintf(`
		CREATE UNIQUE INDEX "%s_date_channel_channel_id_country" ON "public"."%s" USING btree (
		"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
		"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_country" ON "public"."%s" USING btree (
			"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_channel" ON "public"."%s" USING btree (
			"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_channel_state_page_views" ON "public"."%s" USING btree (
			"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
			"page_views" "pg_catalog"."int8_ops" desc NULLS LAST
		);
		CREATE INDEX "%s_account" ON "public"."%s" USING btree (
			"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_platform" ON "public"."%s" USING btree (
			"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
ALTER TABLE "public"."%s" ADD CONSTRAINT "%s__pkey" PRIMARY KEY ("id","date");
	`, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
	_, err = r.data.stddb.Exec(sqlIndex)
	if err != nil {
		r.log.Errorf("[createGameCountryPartitionTable]: CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) createGameCountryPartitionChildTables(year int) error {
	yearTables := getYearlyTables(gamecountrypartition.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {

		// 查询数据表是否存在
		exist := false
		var err error
		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, gamecountrypartition.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}
		//tableName := monthTable
		//sqlIndex := fmt.Sprintf(`
		//	CREATE UNIQUE INDEX "%s_date_channel_channe_id_country" ON "public"."%s" USING btree (
		//	"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
		//	"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		//	);
		//CREATE INDEX "%s_country" ON "public"."%s" USING btree (
		//	"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		//);
		//	CREATE INDEX "%s_channel" ON "public"."%s" USING btree (
		//	"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		//	);
		//	CREATE INDEX "%s_channel_state_page_views" ON "public"."%s" USING btree (
		//	"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		//	"page_views" "pg_catalog"."int8_ops" desc NULLS LAST
		//);
		//`, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
		//
		////	ALTER TABLE "public"."%s" ADD CONSTRAINT "%s_pkey" PRIMARY KEY ("id");
		////ALTER TABLE "public"."%s" ADD CONSTRAINT "%s_pkey" PRIMARY KEY ("id","date");
		//
		//_, err = r.data.stddb.Exec(sqlIndex)
		//if err != nil {
		//	return err
		//}
		monthPo++
	}
	return nil
}

func (r *shardingTablesRepo) createChannelCountryAdFormatHKDPartitionTable() error {
	tableName := channelcountryadformathkdpartition.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."channel_country_ad_format_hkd" (
		"id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
		INCREMENT 1
	MINVALUE  1
	MAXVALUE 9223372036854775807
	START 1
	),
	"date" timestamptz(6) NOT NULL,
		"channel" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"channel_id" varchar COLLATE "pg_catalog"."default",
		"country" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"ad_format" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"estimated_earnings" float8 NOT NULL,
		"page_views_rpm" float8 NOT NULL,
		"page_views" int8 NOT NULL,
		"impressions" int8 NOT NULL,
		"impressions_rpm" float8 NOT NULL,
		"ad_requests_coverage" float8 NOT NULL,
		"clicks" int8 NOT NULL,
		"ad_requests" int8 NOT NULL,
		"impressions_ctr" float8 NOT NULL,
		"active_view_viewability" float8 NOT NULL,
		"cost_per_click" float8 NOT NULL,
		"matched_ad_requests" int8 DEFAULT 0,
		 account    text   default 'pub-****************'::text not null,
         platform   text   default 'Adsense'::text              not null
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	sqlIndex := fmt.Sprintf(`
CREATE INDEX "%s_ad_format" ON "public"."%s" USING btree (
			"ad_format" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
			);
			CREATE INDEX "%s_channel" ON "public"."%s" USING btree (
			"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
			);
			CREATE INDEX "%s_channel_id" ON "public"."%s" USING btree (
			"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
			);
			CREATE INDEX "%s_country" ON "public"."%s" USING btree (
			"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
			);
			CREATE INDEX "%s_date" ON "public"."%s" USING btree (
			"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
			);
		CREATE UNIQUE INDEX "%s_date_channel_channe_id_country_ad_format" ON "public"."%s" USING btree (
		"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
		"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"ad_format" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_account" ON "public"."%s" USING btree (
		"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_platform" ON "public"."%s" USING btree (
		"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
ALTER TABLE "public"."%s" ADD CONSTRAINT "%s__pkey" PRIMARY KEY ("id","date");
	`, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName,
		tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
	_, err = r.data.stddb.Exec(sqlIndex)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) createSiteCountryAdFormatHKDPartitionTable() error {
	tableName := sitecountryadformathkdpartition.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."site_country_ad_format_hkd" (
		 id                      bigint generated by default as identity,
    date                    timestamp with time zone not null,
    site                    varchar                  not null,
    country                 varchar                  not null,
    ad_format               varchar                  not null,
    estimated_earnings      double precision         not null,
    page_views_rpm          double precision         not null,
    page_views              bigint                   not null,
    impressions             bigint                   not null,
    impressions_rpm         double precision         not null,
    ad_requests_coverage    double precision         not null,
    clicks                  bigint                   not null,
    ad_requests             bigint                   not null,
    impressions_ctr         double precision         not null,
    active_view_viewability double precision         not null,
    matched_ad_requests     bigint default 0,
    cost_per_click          double precision         not null,
    account                 text   default 'pub-****************'::text not null,
    platform                text   default 'Adsense'::text              not null
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	sqlIndex := fmt.Sprintf(siteCountryAdFormatIndexSql, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName,
		tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
	_, err = r.data.stddb.Exec(sqlIndex)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) createUrlChannelCountryAdFormatInfoHKDPartitionTable() error {
	tableName := urlchannelcountryadformatinfopartitionhkd.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."url_channel_country_ad_format_info_hkd" (
		"id"  bigint generated by default as identity ,
		 "date" timestamp with time zone NOT NULL ,
		 "url_channel" varchar NOT NULL ,
		 "ad_format" varchar NOT NULL ,
		 "country" varchar NOT NULL ,
		 "estimated_earnings" double precision NOT NULL ,
		 "page_views_rpm" double precision NOT NULL ,
		 "page_views" bigint NOT NULL ,
		 "impressions" bigint NOT NULL ,
		 "impressions_rpm" double precision NOT NULL ,
		 "ad_requests_coverage" double precision NOT NULL ,
		 "clicks" bigint NOT NULL ,
		 "ad_requests" bigint NOT NULL ,
		 "impressions_ctr" double precision NOT NULL ,
		 "active_view_viewability" double precision NOT NULL ,
		 "cost_per_click" double precision NOT NULL ,
		 account                 text default 'pub-****************'::text not null,
          platform                text default 'Adsense'::text              not null,  
		 "matched_ad_requests" bigint NOT NULL ,
		 "site" varchar NOT NULL ,
		 "is_root" boolean NOT NULL ,
		 "sub_channel" varchar NOT NULL ,
		 "page_type" varchar NOT NULL ,
		 "app_id" varchar NOT NULL 
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	sqlIndex := fmt.Sprintf(urlChannelCountryAdFormatInfoIndexSql, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName,
		tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
	_, err = r.data.stddb.Exec(sqlIndex)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) createGameCountryHKDPartitionTable() error {
	tableName := gamecountryhkdpartition.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."game_country_hkd" (
		"id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
		INCREMENT 1
	MINVALUE  1
	MAXVALUE 9223372036854775807
	START 1
	),
	"date" timestamptz(6) NOT NULL,
		"channel" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"channel_id" varchar COLLATE "pg_catalog"."default",
		"country" varchar COLLATE "pg_catalog"."default" NOT NULL,
		"estimated_earnings" float8 NOT NULL,
		"page_views_rpm" float8 NOT NULL,
		"page_views" int8 NOT NULL,
		"impressions" int8 NOT NULL,
		"impressions_rpm" float8 NOT NULL,
		"ad_requests_coverage" float8 NOT NULL,
		"clicks" int8 NOT NULL,
		"ad_requests" int8 NOT NULL,
		"impressions_ctr" float8 NOT NULL,
		"active_view_viewability" float8 NOT NULL,
		"cost_per_click" float8 NOT NULL,
		"matched_ad_requests" int8 NOT NULL,
		account                 text default 'pub-****************'::text not null,
    	platform                text default 'Adsense'::text              not null
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	sqlIndex := fmt.Sprintf(`
		CREATE UNIQUE INDEX "%s_date_channel_channel_id_country" ON "public"."%s" USING btree (
		"date" "pg_catalog"."timestamptz_ops" ASC NULLS LAST,
		"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"channel_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
		"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_country" ON "public"."%s" USING btree (
			"country" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_channel" ON "public"."%s" USING btree (
			"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_channel_state_page_views" ON "public"."%s" USING btree (
			"channel" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
			"page_views" "pg_catalog"."int8_ops" desc NULLS LAST
		);
		CREATE INDEX "%s_account" ON "public"."%s" USING btree (
			"account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
		CREATE INDEX "%s_platform" ON "public"."%s" USING btree (
			"platform" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
		);
ALTER TABLE "public"."%s" ADD CONSTRAINT "%s__pkey" PRIMARY KEY ("id","date");
	`, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
	_, err = r.data.stddb.Exec(sqlIndex)
	if err != nil {
		r.log.Errorf("[createGameCountryHKDPartitionTable]: CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) createChannelCountryAdFormatHKDPartitionChildTables(year int) error {
	yearTables := getYearlyTables(channelcountryadformathkdpartition.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {
		var err error
		// 查询数据表是否存在
		exist := false
		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, channelcountryadformathkdpartition.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}

		monthPo++
	}
	return nil
}

func (r *shardingTablesRepo) createSiteCountryAdFormatHKDPartitionChildTables(year int) error {
	yearTables := getYearlyTables(sitecountryadformathkdpartition.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {

		// 查询数据表是否存在
		exist := false
		var err error

		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, sitecountryadformathkdpartition.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}
		//tableName := monthTable
		//sqlIndex := fmt.Sprintf(siteCountryAdFormatChildIndexSql, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
		//
		//_, err = r.data.stddb.Exec(sqlIndex)
		//if err != nil {
		//	return err
		//}
		monthPo++
	}
	return nil
}

func (r *shardingTablesRepo) createUrlChannelCountryAdFormatHKDPartitionChildTables(year int) error {
	yearTables := getYearlyTables(urlchannelcountryadformatinfopartitionhkd.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {

		// 查询数据表是否存在
		exist := false
		var err error
		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, urlchannelcountryadformatinfopartitionhkd.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}
		//tableName := monthTable

		//sqlIndex := fmt.Sprintf(urlChannelCountryAdFormatInfoChildTableIndexSql, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName, tableName)
		//
		//_, err = r.data.stddb.Exec(sqlIndex)
		//if err != nil {
		//	return err
		//}
		monthPo++
	}
	return nil
}

func (r *shardingTablesRepo) createGameCountryHKDPartitionChildTables(year int) error {
	yearTables := getYearlyTables(gamecountryhkdpartition.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {

		// 查询数据表是否存在
		exist := false
		var err error
		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, gamecountryhkdpartition.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}

		monthPo++
	}
	return nil
}

func (r *shardingTablesRepo) createSiteAdFormatHistoryPartitionTable() error {
	tableName := siteadformathistory.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."site_ad_format_histories" (
		id                      bigint generated by default as identity,
		collected_at 			timestamp with time zone not null,
		date                    timestamp with time zone not null,
		site                    varchar                  not null,
		ad_format               varchar                  not null,
		estimated_earnings      double precision         not null,
		page_views_rpm          double precision         not null,
		page_views              bigint                   not null,
		impressions             bigint                   not null,
		impressions_rpm         double precision         not null,
		ad_requests_coverage    double precision         not null,
		clicks                  bigint                   not null,
		ad_requests             bigint                   not null,
		impressions_ctr         double precision         not null,
		active_view_viewability double precision         not null,
		matched_ad_requests     bigint  				 not null,
		cost_per_click          double precision         not null ,
		active_view_time        bigint default 0         not null,
		account                 text   default 'pub-****************'::text not null,
    	platform                text   default 'Adsense'::text              not null
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	_, err = r.data.stddb.Exec(siteAdFormatHistoryIndexSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) createSiteAdFormatHistoryChildTables(year int) error {
	yearTables := getYearlyTables(siteadformathistory.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {

		// 查询数据表是否存在
		exist := false
		var err error

		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, siteadformathistory.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}
		monthPo++
	}
	return nil
}

func (r *shardingTablesRepo) createSiteBusinessPartitionTable() error {
	tableName := sitebusiness.Table
	exist, err := r.tableExists(tableName)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}

	createSql := `CREATE TABLE "public"."site_businesses" (
		id                      bigint generated by default as identity,
		date                    timestamp with time zone not null,
		site                    varchar                  not null,
		estimated_earnings      double precision         not null,
		page_views_rpm          double precision         not null,
		page_views              bigint                   not null,
		impressions             bigint                   not null,
		impressions_rpm         double precision         not null,
		ad_requests_coverage    double precision         not null,
		clicks                  bigint                   not null,
		ad_requests             bigint                   not null,
		impressions_ctr         double precision         not null,
		active_view_viewability double precision         not null,
		matched_ad_requests     bigint  				 not null,
		cost_per_click          double precision         not null,
		impressions_per_page_view double precision not null
                                        
	)PARTITION BY RANGE (date);`
	_, err = r.data.stddb.Exec(createSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table Error: %s", err.Error())
		return err
	}

	_, err = r.data.stddb.Exec(siteBusinessIndexSql)
	if err != nil {
		r.log.Errorf("CreatePartitionTables Create Table's Index Error: %s", err.Error())
		return err
	}
	return nil
}

func (r *shardingTablesRepo) createSiteBusinessChildTables(year int) error {
	yearTables := getYearlyTables(sitebusiness.Table, year)
	monthPo := time.January
	monthsFirst := partition.GetYearlyMonthDate(year)
	for _, monthTable := range yearTables {

		// 查询数据表是否存在
		exist := false
		var err error

		exist, err = r.tableExists(monthTable)
		if err != nil {
			return err
		}
		if exist {
			continue
		}

		startEnd := monthsFirst[monthPo]
		query := fmt.Sprintf(`CREATE TABLE %s PARTITION OF %s
            FOR VALUES FROM ('%s') TO ('%s');`, monthTable, sitebusiness.Table, startEnd.Start, startEnd.End)
		_, err = r.data.stddb.Exec(query)
		if err != nil {
			return err
		}
		monthPo++
	}
	return nil
}

func getYearlyTables(tableName string, year int) []string {
	tables := make([]string, 0)
	// 按月迭代计算分表
	for month := time.January; month <= time.December; month++ {
		tabName := fmt.Sprintf("%s_y%dm%02d", tableName, year, month)
		tables = append(tables, tabName)
	}

	return tables
}

func NewShardingTablesRepo(data *Data, logger log.Logger) biz.ShardingSchemaRepo {
	return &shardingTablesRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/shardingTableRepo")),
	}
}
