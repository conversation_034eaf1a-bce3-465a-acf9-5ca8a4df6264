package data

import (
	"context"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"
	analyticsadmin "google.golang.org/api/analyticsadmin/v1alpha"
	analyticsdata "google.golang.org/api/analyticsdata/v1beta"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/cast"
)

type analyticsRepo struct {
	data *Data
	log  *log.Helper
}

func (r *analyticsRepo) ListPropertiesID(ctx context.Context, accountIds []string) ([]string, error) {
	analyticsService := r.data.gsm.GetAnalyticsService()
	if analyticsService == nil {
		return nil, fmt.Errorf("analytics服务未初始化")
	}

	propertiesService := analyticsadmin.NewPropertiesService(analyticsService.analyticsAdmin)
	propertiesIDs := make([]string, 0)
	for _, acc := range accountIds {
		propertiesReply, err := propertiesService.List().Filter(fmt.Sprintf("parent:accounts/%s", acc)).PageSize(100).Context(ctx).Do()
		if err != nil {
			r.log.Errorf("list properties err %v", err)
			return nil, err
		}
		for _, pro := range propertiesReply.Properties {
			propertiesIDs = append(propertiesIDs, strings.ReplaceAll(pro.Name, "properties/", ""))
		}

		// 处理分页
		nextPageToken := propertiesReply.NextPageToken
		for nextPageToken != "" {
			propertiesReply, err = propertiesService.List().
				Filter(fmt.Sprintf("parent:accounts/%s", acc)).
				PageSize(100).
				PageToken(nextPageToken).Context(ctx).
				Do()
			if err != nil {
				r.log.Errorf("list next page properties err %v", err)
				return nil, err
			}

			for _, pro := range propertiesReply.Properties {
				propertiesIDs = append(propertiesIDs, strings.ReplaceAll(pro.Name, "properties/", ""))
			}

			nextPageToken = propertiesReply.NextPageToken
		}
	}
	return propertiesIDs, nil
}

func (r *analyticsRepo) Save(ctx context.Context, analyticsData []*biz.AnalyticsData) error {
	bulk := make([]*ent.AnalyticsCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}

	for i, data := range analyticsData {
		bulk = append(bulk, tx.Analytics.Create().SetDate(data.Date).
			SetPageViews(data.PageViews).SetActiveUsers(data.ActiveUsers).
			SetTotalUsers(data.TotalUsers).SetNewUsers(data.NewUsers).SetPlayCount(data.PlayCount).SetPropertiesID(data.PropertiesID).
			SetGameImpression(data.GameImpression).SetUpu(data.Upu).SetPaymentTimes(data.PaymentTimes).
			SetAverageSessionDuration(data.AverageSessionDuration).SetScreenPageViewsPerUser(data.ScreenPageViewsPerUser).
			SetAverageEngagementTimePerActiveUser(data.AverageEngagementTimePerActiveUser))
		if (i+1)%4000 == 0 || i == len(analyticsData)-1 {
			err := tx.Analytics.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "properties_id")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func (r *analyticsRepo) Query(ctx context.Context, propertiesID, start, end string) ([]*biz.AnalyticsData, error) {
	analyticsService := r.data.gsm.GetAnalyticsService()
	if analyticsService == nil {
		return nil, fmt.Errorf("Analytics服务未初始化")
	}

	resp, err := analyticsService.analytics.Properties.RunReport(fmt.Sprintf("properties/%s", propertiesID), &analyticsdata.RunReportRequest{
		DateRanges: []*analyticsdata.DateRange{
			{
				EndDate:   end,
				StartDate: start,
			},
		},
		Limit: limit,
		Dimensions: []*analyticsdata.Dimension{
			{
				Name: DimensionDate,
			},
		},
		Metrics: []*analyticsdata.Metric{
			{
				Name: MetricActiveUsers,
			},
			{
				Name: MetricAverageSessionDuration,
			},
			{
				Name: MetricNewUsers,
			},
			{
				Name: MetricScreenPageViews,
			},
			{
				Name: MetricTotalUsers,
			},
			{Name: MetricUserEngagementDuration},
			{Name: MetricScreenPageViewsPerUser},
		},
	}).Context(ctx).Do()
	if err != nil {
		return nil, err
	}
	gamePlayMap, err := r.getGamePlay(ctx, propertiesID, start, end)
	if err != nil {
		return nil, err
	}
	var all []*biz.AnalyticsData

	for _, row := range resp.Rows {
		date, err := time.Parse("20060102", row.DimensionValues[0].Value)
		if err != nil {
			return nil, err
		}
		activeUsers := cast.StringToInt(row.MetricValues[0].Value)
		userEngagementDuration := 0.0
		if activeUsers != 0 {
			userEngagementDuration = cast.StringToFloat(row.MetricValues[5].Value) / float64(activeUsers)
		}
		a := &biz.AnalyticsData{
			Date:                               date,
			PropertiesID:                       propertiesID,
			PageViews:                          cast.StringToInt(row.MetricValues[3].Value),
			ActiveUsers:                        activeUsers,
			TotalUsers:                         cast.StringToInt(row.MetricValues[4].Value),
			NewUsers:                           cast.StringToInt(row.MetricValues[2].Value),
			AverageSessionDuration:             cast.StringToFloat(row.MetricValues[1].Value),
			PlayCount:                          gamePlayMap[row.DimensionValues[0].Value],
			ScreenPageViewsPerUser:             cast.StringToFloat(row.MetricValues[6].Value),
			AverageEngagementTimePerActiveUser: userEngagementDuration,
		}
		all = append(all, a)
	}
	return all, nil
}

func (r *analyticsRepo) getGamePlay(ctx context.Context, propertiesID, start, end string) (map[string]int, error) {
	analyticsService := r.data.gsm.GetAnalyticsService()
	if analyticsService == nil {
		return nil, fmt.Errorf("Analytics服务未初始化")
	}

	resp, err := analyticsService.analytics.Properties.RunReport(fmt.Sprintf("properties/%s", propertiesID), &analyticsdata.RunReportRequest{
		DateRanges: []*analyticsdata.DateRange{{EndDate: end, StartDate: start}},
		DimensionFilter: &analyticsdata.FilterExpression{
			Filter: &analyticsdata.Filter{
				FieldName: DimensionUnifiedPagePathScreen,
				StringFilter: &analyticsdata.StringFilter{
					MatchType: "FULL_REGEXP",
					Value:     "^(/[a-z]{2})?(/pc)?/game/[\\w-]+/(?:play(?:/[a-z]+)?|info(?:/recommended)?|offplay|match/\\d+)$",
				},
			},
		},
		Dimensions: []*analyticsdata.Dimension{{Name: DimensionDate}},
		Metrics:    []*analyticsdata.Metric{{Name: MetricScreenPageViews}},
		Limit:      limit,
	}).Context(ctx).Do()
	if err != nil {
		return nil, err
	}
	gamePlayMap := map[string]int{}
	for _, row := range resp.Rows {
		gamePlayMap[row.DimensionValues[0].Value] = cast.StringToInt(row.MetricValues[0].Value)
	}
	return gamePlayMap, nil
}

// NewAnalyticsRepo .
func NewAnalyticsRepo(data *Data, logger log.Logger) biz.AnalyticsRepo {
	return &analyticsRepo{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "adsense-bot/data/analytics")),
	}
}
