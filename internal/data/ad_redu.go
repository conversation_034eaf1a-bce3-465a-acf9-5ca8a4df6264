package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
)

type adReductionRepo struct {
	data *Data
	log  *log.Helper
}

func (r *adReductionRepo) Save(ctx context.Context, data []*biz.AdReduction) error {

	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	bulk := make([]*ent.AdReductionCreate, 0)
	for i, d := range data {
		bulk = append(bulk, tx.AdReduction.Create().SetDate(d.Date).SetSite(d.Site).
			SetReductionDate(d.ReductionDate).SetDeltaDay(d.DeltaDay).SetReductionType(d.ReductionType).
			SetReductionRateR(d.ReductionRateR).SetReductionRateI(d.ReductionRateI).
			SetReductionRateC(d.ReductionRateC).SetReductionRateE(d.ReductionRateE).
			SetReductionChangeR(d.ReductionChangeR).SetReductionChangeI(d.ReductionChangeI).
			SetReductionChangeC(d.ReductionChangeC).SetReductionChangeE(d.ReductionChangeE).
			SetAccount(d.Account).SetPlatform(d.Platform))
		if (i+1)%500 == 0 || i == len(data)-1 {
			err = tx.AdReduction.CreateBulk(bulk...).Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}

			bulk = bulk[:0]
		}
	}

	return tx.Commit()

}

func NewAdReductionRepo(data *Data, logger log.Logger) biz.AdReductionRepo {
	return &adReductionRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/ad_reduction")),
	}
}
