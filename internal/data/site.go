package data

import (
	"context"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/cooperationsiteurlchannel"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/site"
)

type siteRepo struct {
	data *Data
	log  *log.Helper
}

func (r *siteRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseSiteData) error {
	bulk := make([]*ent.SiteHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	//s, e := date.GetDateRange("LAST_7_DAYS", time.Now())
	//_, err = tx.Site.Delete().Where(site.DateGTE(s), site.DateLTE(e)).Exec(ctx)
	//if err != nil {
	//	return rollback(tx, err)
	//}
	for i, data := range datas {
		bulk = append(bulk, tx.SiteHKD.Create().SetDate(data.Date).SetSite(data.Site).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.SiteHKD.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "site", "account", "platform")).UpdateNewValues().Exec(ctx)

			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func (r *siteRepo) Query(ctx context.Context, start time.Time, end time.Time) ([]*biz.AdsenseSiteData, error) {

	all, err := r.data.db.Site.Query().Where(site.DateGTE(start), site.DateLTE(end)).All(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdsenseSiteData, 0, len(all))
	for _, v := range all {
		res = append(res, &biz.AdsenseSiteData{
			Date:                  v.Date.Add(8 * time.Hour),
			Site:                  v.Site,
			EstimatedEarnings:     v.EstimatedEarnings,
			PageViews:             v.PageViews,
			PageViewsRpm:          v.PageViewsRpm,
			IMPRESSIONS:           v.Impressions,
			ImpressionsRpm:        v.ImpressionsRpm,
			AdRequestsCoverage:    v.AdRequestsCoverage,
			CLICKS:                v.Clicks,
			AdRequests:            v.AdRequests,
			ImpressionsCtr:        v.ImpressionsCtr,
			ActiveViewViewability: v.ActiveViewViewability,
			CostPerClick:          v.CostPerClick,
			MatchedAdRequests:     v.MatchedAdRequests,
		})
	}
	return res, nil
}

func (r *siteRepo) ListNoCooperationSite(ctx context.Context) ([]string, error) {
	s, err := r.data.db.CooperationSiteUrlChannel.Query().Unique(true).Select(cooperationsiteurlchannel.FieldSite).Strings(ctx)
	if err != nil {
		return nil, err
	}
	sites, err := r.data.db.Site.Query().Where(site.SiteNotIn(s...)).Unique(true).Select(site.FieldSite).Strings(ctx)
	if err != nil {
		return nil, err
	}
	return sites, nil
}

func (r *siteRepo) Create(ctx context.Context, datas []*biz.AdsenseSiteData) error {
	bulk := make([]*ent.SiteCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	//s, e := date.GetDateRange("LAST_7_DAYS", time.Now())
	//_, err = tx.Site.Delete().Where(site.DateGTE(s), site.DateLTE(e)).Exec(ctx)
	//if err != nil {
	//	return rollback(tx, err)
	//}
	for i, data := range datas {
		bulk = append(bulk, tx.Site.Create().SetDate(data.Date).SetSite(data.Site).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err := tx.Site.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "site", "account", "platform")).UpdateNewValues().Exec(ctx)

			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}

func NewSiteRepo(data *Data, logger log.Logger) biz.AdSenseSiteRepo {
	return &siteRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/site")),
	}
}
