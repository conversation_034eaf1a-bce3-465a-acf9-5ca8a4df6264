package data

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/urlutil"
)

type pageURLCountryAdFormatRepo struct {
	data *Data
	log  *log.Helper
}

func (r *pageURLCountryAdFormatRepo) Create(ctx context.Context, datas []*biz.AdsensePageURLCountryAdFormatData) error {
	bulkInfo := make([]*ent.PageUrlCountryAdFormatInfoCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		infos := urlutil.FormatURL(data.PageURL)
		bulkInfo = append(bulkInfo, tx.PageUrlCountryAdFormatInfo.Create().SetAdFormat(data.AdFormat).SetCountry(data.Country).
			SetDate(data.Date).SetPageURL(data.PageURL).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetFrom(infos[1]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err := tx.PageUrlCountryAdFormatInfo.CreateBulk(bulkInfo...).
				OnConflict(sql.ConflictColumns("date", "page_url", "country", "ad_format", "account", "platform")).
				UpdateNewValues().Exec(ctx)

			if err != nil {
				return rollback(tx, err)
			}

			bulkInfo = bulkInfo[:0]
		}
	}

	return tx.Commit()
}

func NewPageURLCountryAdFormatRepo(data *Data, logger log.Logger) biz.AdSensePageURLCountryAdFormatRepo {
	return &pageURLCountryAdFormatRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/pageURL/country/adformat")),
	}
}
