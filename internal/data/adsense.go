package data

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/date"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/api/adsense/v2"
	"google.golang.org/api/googleapi"
)

type adsenseRepo struct {
	data          *Data
	log           *log.Helper
	adsenseConfig []*conf.GoogleConfig
}

func (r *adsenseRepo) Generate(ctx context.Context, pubID string, metrics, orderBy, dimensions, filters []string, dateRange, currencyCode string) (*biz.Adsense, error) {
	// 获取指定pubID的GoogleService
	service, err := r.data.gsm.GetService(pubID)
	if err != nil {
		return nil, fmt.Errorf("获取GoogleService失败: %w", err)
	}

	//metrics := []string{"ESTIMATED_EARNINGS", "PAGE_VIEWS", "PAGE_VIEWS_RPM", "IMPRESSIONS", "IMPRESSIONS_RPM", "AD_REQUESTS_COVERAGE", "CLICKS", "AD_REQUESTS", "IMPRESSIONS_CTR", "ACTIVE_VIEW_VIEWABILITY"}
	do, err := service.adsense.Accounts.Reports.Generate(fmt.Sprintf("accounts/%s", pubID)).
		Metrics(metrics...).
		Dimensions(dimensions...).
		CurrencyCode(currencyCode).
		//StartDateYear(int64(r.adsense.StartYear)).StartDateMonth(int64(r.adsense.StartMonth)).StartDateDay(int64(r.adsense.StartDay)).
		//EndDateMonth(int64(r.adsense.GetEndMonth())).EndDateYear(int64(r.adsense.EndYear)).EndDateDay(int64(r.adsense.EndDay)).
		//OrderBy("-AD_REQUESTS_COVERAGE").
		OrderBy(orderBy...).
		Filters(filters...).
		DateRange(dateRange).Context(ctx).Do()
	if err != nil {
		if gErr, ok := err.(*googleapi.Error); ok {
			r.log.WithContext(ctx).Errorf("Status code: %v", gErr.Code)
			r.log.WithContext(ctx).Errorf("err message :%s ", gErr.Message)
			r.log.WithContext(ctx).Errorf("err details :%v ", gErr.Details)
			r.log.WithContext(ctx).Errorf("err body :%v ", gErr.Body)
		}
		return nil, err
	}
	//r.log.WithContext(ctx).Infof("generate :%v", do)

	r.log.WithContext(ctx).Infof("status code : %d", do.HTTPStatusCode)
	r.log.WithContext(ctx).Infof("total matches : %d", do.TotalMatchedRows)
	//body, err := do.MarshalJSON()
	//if err != nil {
	//	return nil, err
	//}
	//r.log.WithContext(ctx).Infof("response :%s", string(body))
	return r.toAdsense(do), nil
}

func (r *adsenseRepo) GenerateCsvByCustomDateRange(ctx context.Context, pubID string, metrics, orderBy, dimensions, filters []string, currencyCode string, dateRange *date.Range) (*biz.Adsense, error) {
	// 获取指定pubID的GoogleService
	service, err := r.data.gsm.GetService(pubID)
	if err != nil {
		return nil, fmt.Errorf("获取GoogleService失败: %w", err)
	}

	URL, err := url.Parse(fmt.Sprintf("https://adsense.googleapis.com/v2/accounts/%s/reports:generateCsv", pubID))
	if err != nil {
		return nil, err
	}
	query := URL.Query()
	for _, metric := range metrics {
		query.Add("metrics", metric)
	}
	for _, dimension := range dimensions {
		query.Add("dimensions", dimension)
	}
	for _, order := range orderBy {
		query.Add("orderBy", order)
	}
	for _, filter := range filters {
		query.Add("filters", filter)
	}

	// 设置自定义日期范围
	query.Set("dateRange", "CUSTOM")
	query.Set("currencyCode", currencyCode)

	// 设置开始日期
	startDate := dateRange.StartTime
	query.Set("startDate.day", fmt.Sprintf("%d", startDate.Day()))
	query.Set("startDate.month", fmt.Sprintf("%d", startDate.Month()))
	query.Set("startDate.year", fmt.Sprintf("%d", startDate.Year()))

	// 设置结束日期
	endDate := dateRange.EndTime
	query.Set("endDate.day", fmt.Sprintf("%d", endDate.Day()))
	query.Set("endDate.month", fmt.Sprintf("%d", endDate.Month()))
	query.Set("endDate.year", fmt.Sprintf("%d", endDate.Year()))

	URL.RawQuery = query.Encode()
	r.log.Info(URL.String())
	resp, err := service.client.Get(URL.String())
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		bodyData, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		return nil, fmt.Errorf("status code : %d,body: %s", resp.StatusCode, string(bodyData))
	}
	r.log.WithContext(ctx).Infof("status code : %d", resp.StatusCode)

	toAdsense, err := r.csvToAdsense(resp.Body)
	if err != nil {
		return nil, err
	}
	r.log.WithContext(ctx).Infof("total matches : %d", len(toAdsense.Data))

	return toAdsense, nil
}

func (r *adsenseRepo) GenerateCsv(ctx context.Context, pubID string, metrics, orderBy, dimensions, filters []string, dateRange, currencyCode string) (*biz.Adsense, error) {
	// 获取指定pubID的GoogleService
	service, err := r.data.gsm.GetService(pubID)
	if err != nil {
		return nil, fmt.Errorf("获取GoogleService失败: %w", err)
	}

	URL, err := url.Parse(fmt.Sprintf("https://adsense.googleapis.com/v2/accounts/%s/reports:generateCsv", pubID))
	if err != nil {
		return nil, err
	}
	query := URL.Query()
	for _, metric := range metrics {
		query.Add("metrics", metric)
	}
	for _, dimension := range dimensions {
		query.Add("dimensions", dimension)
	}
	for _, order := range orderBy {
		query.Add("orderBy", order)
	}
	for _, filter := range filters {
		query.Add("filters", filter)
	}
	query.Set("dateRange", dateRange)
	query.Set("currencyCode", currencyCode)

	URL.RawQuery = query.Encode()
	r.log.Info(URL.String())
	resp, err := service.client.Get(URL.String())
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		bodyData, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		return nil, fmt.Errorf("status code : %d,body: %s", resp.StatusCode, string(bodyData))
	}
	r.log.WithContext(ctx).Infof("status code : %d", resp.StatusCode)

	toAdsense, err := r.csvToAdsense(resp.Body)
	if err != nil {
		return nil, err
	}
	r.log.WithContext(ctx).Infof("total matches : %d", len(toAdsense.Data))

	return toAdsense, nil
}

func (r *adsenseRepo) toAdsense(do *adsense.ReportResult) *biz.Adsense {
	if do == nil {
		return &biz.Adsense{
			Averages: struct {
				Cells []struct {
					Value string `json:"value,omitempty"`
				} `json:"cells"`
			}{},
			EndDate: struct {
				Day   int `json:"day"`
				Month int `json:"month"`
				Year  int `json:"year"`
			}{},
			Headers: nil,
			//Rows:    nil,
			StartDate: struct {
				Day   int `json:"day"`
				Month int `json:"month"`
				Year  int `json:"year"`
			}{},
			TotalMatchedRows: "",
			Totals: struct {
				Cells []struct {
					Value string `json:"value,omitempty"`
				} `json:"cells"`
			}{},
			Data: make([][]string, 0),
		}
	}
	data := make([][]string, len(do.Rows))
	for j, row := range do.Rows {
		d := make([]string, len(row.Cells))
		for i, cell := range row.Cells {
			//rows[j].Cells[i].Value = cell.Value
			d[i] = cell.Value
			//rows[i].Cells[len(rows[i].Cells)-1].Value = cell.Value
		}
		data[j] = d
	}
	return &biz.Adsense{
		//Averages: struct {
		//	Cells []struct {
		//		Value string `json:"value,omitempty"`
		//	} `json:"cells"`
		//}{
		//	cells,
		//},
		EndDate: struct {
			Day   int `json:"day"`
			Month int `json:"month"`
			Year  int `json:"year"`
		}{
			Day:   int(do.EndDate.Day),
			Month: int(do.EndDate.Month),
			Year:  int(do.EndDate.Year),
		},
		//Headers: headers,
		//Rows: rows,
		StartDate: struct {
			Day   int `json:"day"`
			Month int `json:"month"`
			Year  int `json:"year"`
		}{
			Day:   int(do.StartDate.Day),
			Month: int(do.StartDate.Month),
			Year:  int(do.StartDate.Year),
		},
		TotalMatchedRows: strconv.FormatInt(do.TotalMatchedRows, 10),
		Data:             data,
		//Totals: struct {
		//	Cells []struct {
		//		Value string `json:"value,omitempty"`
		//	} `json:"cells"`
		//}{totalCells},
	}
}

func (r *adsenseRepo) ListUrlChannels(ctx context.Context, pubID string) ([]*biz.AdsenseUrlChannel, error) {
	// 获取指定pubID的GoogleService
	service, err := r.data.gsm.GetService(pubID)
	if err != nil {
		return nil, fmt.Errorf("获取GoogleService失败: %w", err)
	}

	parent := fmt.Sprintf("accounts/%s/adclients/ca-%s", pubID, pubID)
	do, err := service.adsense.Accounts.Adclients.Urlchannels.
		List(parent).Context(ctx).Do()
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdsenseUrlChannel, 0)
	for _, ch := range do.UrlChannels {
		res = append(res, &biz.AdsenseUrlChannel{
			Name:                 ch.Name,
			ReportingDimensionId: ch.ReportingDimensionId,
			UriPattern:           ch.UriPattern,
		})
	}
	// do.
	for {
		if do.NextPageToken == "" {
			break
		}
		do, err = service.adsense.Accounts.Adclients.Urlchannels.List(parent).PageToken(do.NextPageToken).Do()
		if err != nil {
			return nil, err
		}
		for _, ch := range do.UrlChannels {
			res = append(res, &biz.AdsenseUrlChannel{
				Name:                 ch.Name,
				ReportingDimensionId: ch.ReportingDimensionId,
				UriPattern:           ch.UriPattern,
			})
		}
	}
	return res, nil
}

func (r *adsenseRepo) ListCustomChannels(ctx context.Context, pubID string) ([]*biz.AdsenseCustomChannel, error) {
	// 获取指定pubID的GoogleService
	service, err := r.data.gsm.GetService(pubID)
	if err != nil {
		return nil, fmt.Errorf("获取GoogleService失败: %w", err)
	}

	parent := fmt.Sprintf("accounts/%s/adclients/ca-%s", pubID, pubID)

	do, err := service.adsense.Accounts.Adclients.Customchannels.List(parent).Context(ctx).Do()
	if err != nil {
		return nil, err
	}
	res := make([]*biz.AdsenseCustomChannel, 0)
	for _, ch := range do.CustomChannels {
		res = append(res, &biz.AdsenseCustomChannel{
			Name:                 ch.Name,
			ReportingDimensionId: ch.ReportingDimensionId,
			DisplayName:          ch.DisplayName,
			Active:               ch.Active,
		})
	}
	// do.
	for {
		if do.NextPageToken == "" {
			break
		}
		do, err = service.adsense.Accounts.Adclients.Customchannels.List(parent).PageToken(do.NextPageToken).Do()
		if err != nil {
			return nil, err
		}
		for _, ch := range do.CustomChannels {
			res = append(res, &biz.AdsenseCustomChannel{
				Name:                 ch.Name,
				ReportingDimensionId: ch.ReportingDimensionId,
				DisplayName:          ch.DisplayName,
				Active:               ch.Active,
			})
		}
	}
	return res, nil
}

func (r *adsenseRepo) csvToAdsense(adsenseData io.Reader) (*biz.Adsense, error) {
	reader := csv.NewReader(adsenseData)

	// 读取所有记录
	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	return &biz.Adsense{
		Data: records[1:],
	}, nil
}

func (r *adsenseRepo) GetAllPubIDs() []string {
	if r.data.gsm == nil {
		return []string{}
	}
	return r.data.gsm.GetAllPubIDs()
}

func (r *adsenseRepo) GetPubIDsForJob(jobConfig *conf.Job_JobConfig) ([]string, error) {
	if r.data.gsm == nil {
		return nil, fmt.Errorf("GoogleServiceManager未初始化")
	}

	// 获取执行模式，如果未设置则默认为ALL模式
	executionMode := jobConfig.GetExecutionMode()
	if executionMode == conf.AccountExecutionMode_UNSPECIFIED {
		executionMode = conf.AccountExecutionMode_ALL
		r.log.Infof("任务 %s 未设置execution_mode，默认使用ALL模式", jobConfig.GetName())
	}

	switch executionMode {
	case conf.AccountExecutionMode_SINGLE:
		// 单账号模式
		if jobConfig.GetPubId() == "" {
			return nil, fmt.Errorf("单账号模式下pub_id不能为空，请在任务配置中指定pub_id")
		}
		// 验证账号是否存在
		_, err := r.data.gsm.GetService(jobConfig.GetPubId())
		if err != nil {
			// 获取所有可用账号用于提示
			availablePubIDs := r.data.gsm.GetEnabledPubIDs()
			return nil, fmt.Errorf("指定的账号不存在或未启用: %s，可用账号: %v",
				jobConfig.GetPubId(), availablePubIDs)
		}
		r.log.Infof("单账号模式验证通过，账号: %s", jobConfig.GetPubId())
		return []string{jobConfig.GetPubId()}, nil

	case conf.AccountExecutionMode_MULTIPLE:
		// 多账号模式
		if len(jobConfig.GetPubIds()) == 0 {
			return nil, fmt.Errorf("多账号模式下pub_ids不能为空，请在任务配置中指定pub_ids列表")
		}
		// 验证所有账号是否存在
		var validPubIDs []string
		var invalidPubIDs []string

		for _, pubID := range jobConfig.GetPubIds() {
			_, err := r.data.gsm.GetService(pubID)
			if err != nil {
				r.log.Warnf("跳过不存在或未启用的账号: %s", pubID)
				invalidPubIDs = append(invalidPubIDs, pubID)
				continue
			}
			validPubIDs = append(validPubIDs, pubID)
		}

		if len(validPubIDs) == 0 {
			availablePubIDs := r.data.gsm.GetEnabledPubIDs()
			return nil, fmt.Errorf("没有有效的账号，指定的账号: %v，无效账号: %v，可用账号: %v",
				jobConfig.GetPubIds(), invalidPubIDs, availablePubIDs)
		}

		if len(invalidPubIDs) > 0 {
			r.log.Warnf("多账号模式部分账号无效，有效账号: %v，无效账号: %v", validPubIDs, invalidPubIDs)
		}

		return validPubIDs, nil

	case conf.AccountExecutionMode_ALL:
		// 所有账号模式
		pubIDs := r.data.gsm.GetEnabledPubIDs()
		if len(pubIDs) == 0 {
			return nil, fmt.Errorf("没有启用的账号，请检查Google配置文件中是否有enabled=true的账号配置")
		}
		r.log.Infof("任务 %s 使用ALL模式，将处理 %d 个账号: %v", jobConfig.GetName(), len(pubIDs), pubIDs)
		return pubIDs, nil

	default:
		return nil, fmt.Errorf("不支持的执行模式: %v", executionMode)
	}
}

// NewAdsenseRepo .
func NewAdsenseRepo(googleConf *conf.Google, data *Data, logger log.Logger) biz.AdsenseRepo {
	return &adsenseRepo{
		data:          data,
		adsenseConfig: googleConf.GetConfigs(),
		log:           log.NewHelper(log.With(logger, "module", "adsense-bot/data/adsense")),
	}
}
