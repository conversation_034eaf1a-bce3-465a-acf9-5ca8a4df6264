package data

import (
	"context"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/predicate"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitecountryadformatpartition"
)

type siteCountryAdFormatPartitionRepo struct {
	data *Data
	log  *log.Helper
}

func (r *siteCountryAdFormatPartitionRepo) CreateHKD(ctx context.Context, datas []*biz.AdsenseSiteCountryAdFormatData) error {
	bulk := make([]*ent.SiteCountryAdFormatHKDPartitionCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}

	for i, data := range datas {
		bulk = append(bulk, tx.SiteCountryAdFormatHKDPartition.Create().SetDate(data.Date).SetSite(data.Site).
			SetCountry(data.Country).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).SetAdFormat(data.AdFormat).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%3500 == 0 || i == len(datas)-1 {
			err = tx.SiteCountryAdFormatHKDPartition.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "site", "country", "ad_format", "account", "platform")).UpdateNewValues().Exec(ctx)

			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}
	return tx.Commit()
}

func formatCooperationSqlAgg(dimensions []string) []string {
	var agg []string
	for _, field := range AggField {
		agg = append(agg, sql.As(sql.Sum(field), field))
	}
	agg = append(agg, sql.As("CAST(ROUND(SUM(estimated_earnings)::numeric, 2) AS numeric(20, 2))", "estimated_earnings"))
	for _, dimension := range dimensions {
		if dimension == "cooperation" {
			agg = append(agg, "cooperation_channel")
		} else if dimension == "site" {
			agg = append(agg, sql.Table(sitecountryadformatpartition.Table).C(sitecountryadformatpartition.FieldSite))
		} else {
			agg = append(agg, dimension)
		}
	}
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(clicks)*1.0, 0.0), 0)AS numeric(20,4)) ", "cost_per_click"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(clicks) / NULLIF(SUM(impressions)*1.0, 0.0), 0)AS numeric(20,4))", "impressions_ctr"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(matched_ad_requests) / NULLIF(SUM(ad_requests)*1.0, 0.0), 0)AS numeric(20,4))", "ad_requests_coverage"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(impressions)*1.0, 0.0)*1000, 0)AS numeric(20,4))", "impressions_rpm"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(estimated_earnings) / NULLIF(SUM(page_views)*1.0, 0.0)*1000, 0)AS numeric(20,4))", "page_views_rpm"))
	//agg = append(agg, SumField...)
	agg = append(agg, sql.As("sum(0.0)", "active_view_viewability"))
	agg = append(agg, sql.As(" CAST(COALESCE(SUM(impressions) / NULLIF(SUM(page_views)*1.0, 0.0), 0)AS numeric(20,4))", "impressions_per_page_view"))

	return agg
}

func (r *siteCountryAdFormatPartitionRepo) Get(ctx context.Context, metrics []string, dimensions []string, site []string, dr biz.DateRange) ([]map[string]interface{}, error) {
	agg := formatCooperationSqlAgg(dimensions)
	metrics = append(metrics, dimensions...)
	ps := []predicate.SiteCountryAdFormatPartition{sitecountryadformatpartition.DateGTE(dr.Start), sitecountryadformatpartition.DateLTE(dr.End)}
	if len(site) > 0 {
		ps = append(ps, sitecountryadformatpartition.SiteIn(site...))
	}

	var all []biz.AdsenseData
	query := r.data.db.SiteCountryAdFormatPartition.Query().Where(
		ps...,
	)

	err := query.Modify(func(s *sql.Selector) {
		s.Select(agg...).
			GroupBy(dimensions...)
	}).
		Scan(ctx, &all)
	if err != nil {
		return nil, err
	}
	var result []map[string]interface{}
	for _, v := range all {
		r := make(map[string]interface{})
		for _, m := range metrics {
			switch m {
			case "site":
				r[m] = v.Site
			case "country":
				r[m] = v.Country
			case "ad_format":
				r[m] = v.AdFormat
			case "date":
				r[m] = v.Date.Add(8 * time.Hour).Format(time.DateOnly)
			case "estimated_earnings":
				r[m] = v.EstimatedEarnings
			case "page_views":
				r[m] = v.PageViews
			case "page_views_rpm":
				r[m] = v.PageViewsRpm
			case "impressions":
				r[m] = v.IMPRESSIONS
			case "impressions_rpm":
				r[m] = v.ImpressionsRpm
			case "ad_requests_coverage":
				r[m] = v.AdRequestsCoverage
			case "clicks":
				r[m] = v.CLICKS
			case "ad_requests":
				r[m] = v.AdRequests
			case "impressions_ctr":
				r[m] = v.ImpressionsCtr
			case "active_view_viewability":
				r[m] = v.ActiveViewViewability
			case "cost_per_click":
				r[m] = v.CostPerClick
			case "matched_ad_requests":
				r[m] = v.MatchedAdRequests
			case "impressions_per_page_view":
				r[m] = v.ImpressionsPerPageView
			}
		}
		result = append(result, r)

	}
	return result, nil
}
func (r *siteCountryAdFormatPartitionRepo) processBatch(ctx context.Context, batch []*biz.AdsenseSiteCountryAdFormatData) error {
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}

	bulk := make([]*ent.SiteCountryAdFormatPartitionCreate, 0, len(batch))
	for _, data := range batch {
		bulk = append(bulk, tx.SiteCountryAdFormatPartition.Create().
			SetDate(data.Date).
			SetSite(data.Site).
			SetCountry(data.Country).
			SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).
			SetPageViewsRpm(data.PageViewsRpm).
			SetAdFormat(data.AdFormat).
			SetImpressions(data.IMPRESSIONS).
			SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).
			SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).
			SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
	}

	err = tx.SiteCountryAdFormatPartition.CreateBulk(bulk...).
		OnConflict(sql.ConflictColumns("date", "site", "country", "ad_format", "account", "platform")).
		UpdateNewValues().
		Exec(ctx)

	if err != nil {
		return rollback(tx, err)
	}

	return tx.Commit()
}
func (r *siteCountryAdFormatPartitionRepo) Create(ctx context.Context, datas []*biz.AdsenseSiteCountryAdFormatData) error {
	// Define smaller batch size
	const batchSize = 3000

	// Process data in batches
	for i := 0; i < len(datas); i += batchSize {
		end := i + batchSize
		if end > len(datas) {
			end = len(datas)
		}

		// Create new transaction for each batch
		if err := r.processBatch(ctx, datas[i:end]); err != nil {
			return err
		}
	}
	return nil
}

func NewSiteCountryAdFormatPartitionRepo(data *Data, logger log.Logger) biz.AdSenseSiteCountryAdFormatRepo {
	return &siteCountryAdFormatPartitionRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/site/country")),
	}
}
