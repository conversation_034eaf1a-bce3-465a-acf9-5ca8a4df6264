package data

import (
	"context"
	"strconv"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/urlutil"
)

type urlChannelCountryRepo struct {
	data *Data
	log  *log.Helper
}

func (r *urlChannelCountryRepo) CreateHKDData(ctx context.Context, datas []*biz.AdsenseURLChannelCountryData) error {
	bulkInfo := make([]*ent.UrlChannelInfoHKDCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		infos := urlutil.FormatURL("https://" + data.UrlChannel)
		isRoot, _ := strconv.ParseBool(infos[5])
		bulkInfo = append(bulkInfo, tx.UrlChannelInfoHKD.Create().SetDate(data.Date).SetURLChannel(data.UrlChannel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetCountry(data.Country).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).SetIsRoot(isRoot).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			err = tx.UrlChannelInfoHKD.CreateBulk(bulkInfo...).OnConflict(sql.ConflictColumns("date", "url_channel", "country", "account", "platform")).UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulkInfo = bulkInfo[:0]
		}
	}
	return tx.Commit()
}

func (r *urlChannelCountryRepo) Create(ctx context.Context, datas []*biz.AdsenseURLChannelCountryData) error {
	//bulk := make([]*ent.UrlChannelCreate, 0)
	bulkInfo := make([]*ent.UrlChannelInfoCreate, 0)
	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		//bulk = append(bulk, tx.UrlChannel.Create().SetDate(data.Date).SetURLChannel(data.UrlChannel).SetEstimatedEarnings(data.EstimatedEarnings).
		//	SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
		//	SetCountry(data.Country).
		//	SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
		//	SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
		//	SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
		//	SetActiveViewViewability(data.ActiveViewViewability).
		//	SetMatchedAdRequests(data.MatchedAdRequests).
		//	SetCostPerClick(data.CostPerClick))
		infos := urlutil.FormatURL("https://" + data.UrlChannel)
		isRoot, _ := strconv.ParseBool(infos[5])
		bulkInfo = append(bulkInfo, tx.UrlChannelInfo.Create().SetDate(data.Date).SetURLChannel(data.UrlChannel).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetCountry(data.Country).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetSite(infos[0]).SetSubChannel(infos[2]).SetPageType(infos[3]).SetAppID(infos[4]).SetIsRoot(isRoot).
			SetCostPerClick(data.CostPerClick).
			SetAccount(data.Account).SetPlatform(data.Platform))
		if (i+1)%2000 == 0 || i == len(datas)-1 {
			//err := tx.UrlChannel.CreateBulk(bulk...).OnConflict(sql.ConflictColumns("date", "url_channel", "country")).UpdateNewValues().Exec(ctx)

			// _, err := tx.UrlChannel.createBulk(bulk...).Save(ctx)
			//if err != nil {
			//	return rollback(tx, err)
			//}
			err = tx.UrlChannelInfo.CreateBulk(bulkInfo...).OnConflict(sql.ConflictColumns("date", "url_channel", "country", "account", "platform")).UpdateNewValues().Exec(ctx)

			// _, err = tx.UrlChannelInfo.createBulk(bulkInfo...).Save(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			//bulk = bulk[:0]
			bulkInfo = bulkInfo[:0]
		}
	}
	return tx.Commit()
}

func NewUrlChannelCountryRepo(data *Data, logger log.Logger) biz.AdSenseURLChannelCountryRepo {
	return &urlChannelCountryRepo{
		data,
		log.NewHelper(log.With(logger, "module", "data/urlChannel/country")),
	}
}
