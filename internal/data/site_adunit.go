package data

import (
	"context"

	entsql "entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
)

type siteAdUnitRepo struct {
	data *Data
	log  *log.Helper
}

func (r *siteAdUnitRepo) Create(ctx context.Context, datas []*biz.AdsenseSiteAdUnitData) error {
	bulk := make([]*ent.SiteAdUnitCreate, 0)

	tx, err := r.data.db.Tx(ctx)
	if err != nil {
		return err
	}
	for i, data := range datas {
		bulk = append(bulk, tx.SiteAdUnit.Create().SetDate(data.Date).SetSite(data.Site).
			SetAdUnit(data.AdUnit).SetEstimatedEarnings(data.EstimatedEarnings).
			SetPageViews(data.PageViews).SetPageViewsRpm(data.PageViewsRpm).
			SetImpressions(data.IMPRESSIONS).SetImpressionsRpm(data.ImpressionsRpm).
			SetAdRequestsCoverage(data.AdRequestsCoverage).SetClicks(data.CLICKS).
			SetAdRequests(data.AdRequests).SetImpressionsCtr(data.ImpressionsCtr).
			SetActiveViewViewability(data.ActiveViewViewability).
			SetMatchedAdRequests(data.MatchedAdRequests).
			SetCostPerClick(data.CostPerClick).SetActiveViewTime(data.ActiveViewTime).
			SetAccount(data.Account).SetPlatform(data.Platform))

		if (i+1)%4000 == 0 || i == len(datas)-1 {
			err = tx.SiteAdUnit.CreateBulk(bulk...).OnConflict(entsql.ConflictColumns("date", "site", "ad_unit", "account", "platform")).
				UpdateNewValues().Exec(ctx)
			if err != nil {
				return rollback(tx, err)
			}
			bulk = bulk[:0]
		}
	}

	return tx.Commit()
}
func NewSiteAdUnitRepo(data *Data, logger log.Logger) biz.AdSenseSiteAdUnitRepo {
	return &siteAdUnitRepo{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/site/ad_unit")),
	}
}
