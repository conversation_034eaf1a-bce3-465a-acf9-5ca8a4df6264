package service

// 日期范围常量
const (
	DateRangeToday      = "TODAY"
	DateRangeYesterday  = "YESTERDAY"
	DateRangeLast7Days  = "LAST_7_DAYS"
	DateRangeLast15Days = "LAST_15_DAYS"
	DateRangeLast30Days = "LAST_30_DAYS"
)

// AdSense指标常量
const (
	MetricEstimatedEarnings     = "ESTIMATED_EARNINGS"
	MetricPageViews             = "PAGE_VIEWS"
	MetricPageViewsRpm          = "PAGE_VIEWS_RPM"
	MetricImpressions           = "IMPRESSIONS"
	MetricImpressionsRpm        = "IMPRESSIONS_RPM"
	MetricAdRequestsCoverage    = "AD_REQUESTS_COVERAGE"
	MetricClicks                = "CLICKS"
	MetricAdRequests            = "AD_REQUESTS"
	MetricImpressionsCtr        = "IMPRESSIONS_CTR"
	MetricActiveViewViewability = "ACTIVE_VIEW_VIEWABILITY"
	MetricCostPerClick          = "COST_PER_CLICK"
	MetricMatchedAdRequests     = "MATCHED_AD_REQUESTS"
	MetricActiveViewTime        = "ACTIVE_VIEW_TIME"
)

// 维度常量
const (
	DimensionDomainCode        = "DOMAIN_CODE"
	DimensionDate              = "DATE"
	DimensionCountryName       = "COUNTRY_NAME"
	DimensionAdFormatCode      = "AD_FORMAT_CODE"
	DimensionCustomChannelName = "CUSTOM_CHANNEL_NAME"
	DimensionCustomChannelId   = "CUSTOM_CHANNEL_ID"
	DimensionPageUrl           = "PAGE_URL"
	DimensionUrlChannelName    = "URL_CHANNEL_NAME"
	DimensionAdUnitName        = "AD_UNIT_NAME"
)

// 排序常量
const (
	OrderByEstimatedEarningsDesc = "-ESTIMATED_EARNINGS"
)

// 业务常量
const (
	ReportNameDjsTodayData = "djs-today-data"
	SendTime4Hour          = "4HOUR"
)

// Job名称常量
const (
	JobNameSiteCountry               = "site-country"
	JobNamePageUrl                   = "page_url"
	JobNameChannelCountry            = "channel_country"
	JobNameUrlChannelCountry         = "url_channel_country"
	JobNameSite                      = "site"
	JobNameSiteAdFormat              = "site_ad_format"
	JobNameSiteAdUnit                = "site_ad_unit"
	JobNameSiteAdFormatHistory       = "site_ad_format_history"
	JobNameSiteCountryAdFormat       = "site_country_ad_format"
	JobNameChannel                   = "channel"
	JobNameChannelAdFormat           = "channel_ad_format"
	JobNameChannelCountryAdFormat    = "channel_country_ad_format"
	JobNamePageUrlAdFormat           = "page_url_ad_format"
	JobNamePageUrlCountryAdFormat    = "page_url_country_ad_format"
	JobNamePageUrlCountry            = "page_url_country"
	JobNameUrlChannel                = "url_channel"
	JobNameUrlChannelAdFormat        = "url_channel_ad_format"
	JobNameUrlChannelCountryAdFormat = "url_channel_country_ad_format"

	// 游戏相关Job名称
	JobNameGenerateGames           = "generate_games"
	JobNameGenerateGamesHkd        = "generate_games_hkd"
	JobNameGenerateGamesCountry    = "generate_games_country"
	JobNameGenerateGamesCountryHkd = "generate_games_country_hkd"

	// 其他Job名称
	JobNameCustomChannel                     = "custom_channel"
	JobNameAutoCreateSchema                  = "auto_create_schema"
	JobNameGenerateCooperationSiteUrlChannel = "generate_cooperation_site_url_channel"
	JobNameAppCountrySummary                 = "app_country_summary"
	JobNameDjsSiteAdFormatCountryData        = "djsSiteAdFormatCountryData"
	JobNameAnalyticsBase                     = "analytics_base"
	JobNameAnalyticsSite                     = "analytics_site"
	JobNameMergeSite                         = "merge_site"
	JobNameGenerateGaChannelReport           = "generate_ga_channel_report"
	JobNameTgAd                              = "tg_ad"

	JobPurchaseInfo        = "purchase_info"
	JobPurchaseSummaryInfo = "purchase_summary_info"

	JobBusinessSite = "business_site"
)

// DJS特殊指标常量（小写）
const (
	DjsMetricAdRequests         = "ad_requests"
	DjsMetricMatchedAdRequests  = "matched_ad_requests"
	DjsMetricImpressions        = "impressions"
	DjsMetricAdRequestsCoverage = "ad_requests_coverage"
	DjsMetricClicks             = "clicks"
	DjsMetricImpressionsCtr     = "impressions_ctr"
	DjsMetricCostPerClick       = "cost_per_click"
	DjsMetricImpressionsRpm     = "impressions_rpm"
	DjsMetricEstimatedEarnings  = "estimated_earnings"
)

// DJS维度常量（小写）
const (
	DjsDimensionDate     = "date"
	DjsDimensionSite     = "site"
	DjsDimensionAdFormat = "ad_format"
	DjsDimensionCountry  = "country"
)

// 预定义的指标数组
var (
	// 标准AdSense指标集合
	StandardAdSenseMetrics = []string{
		MetricEstimatedEarnings,
		MetricPageViews,
		MetricPageViewsRpm,
		MetricImpressions,
		MetricImpressionsRpm,
		MetricAdRequestsCoverage,
		MetricClicks,
		MetricAdRequests,
		MetricImpressionsCtr,
		MetricActiveViewViewability,
		MetricCostPerClick,
		MetricMatchedAdRequests,
	}
	NewStandardAdSenseMetrics = []string{
		MetricEstimatedEarnings,
		MetricPageViews,
		MetricPageViewsRpm,
		MetricImpressions,
		MetricImpressionsRpm,
		MetricAdRequestsCoverage,
		MetricClicks,
		MetricAdRequests,
		MetricImpressionsCtr,
		MetricActiveViewViewability,
		MetricCostPerClick,
		MetricMatchedAdRequests,
		MetricActiveViewTime,
	}

	// 标准排序方式
	StandardOrderBy = []string{OrderByEstimatedEarningsDesc}

	// 历史数据日期范围
	HistoricalDateRanges = []string{DateRangeLast15Days, DateRangeLast30Days}

	// DJS指标数组
	DjsAdSenseMetrics = []string{
		DjsMetricAdRequests,
		DjsMetricMatchedAdRequests,
		DjsMetricImpressions,
		DjsMetricAdRequestsCoverage,
		DjsMetricClicks,
		DjsMetricImpressionsCtr,
		DjsMetricCostPerClick,
		DjsMetricImpressionsRpm,
		DjsMetricEstimatedEarnings,
	}

	// DJS维度数组
	DjsAdSenseDimensions = []string{
		DjsDimensionDate,
		DjsDimensionSite,
		DjsDimensionAdFormat,
		DjsDimensionCountry,
	}

	// 预定义的维度数组
	SiteDimensions                      = []string{DimensionDomainCode, DimensionDate}
	SiteCountryDimensions               = []string{DimensionDomainCode, DimensionDate, DimensionCountryName}
	PageUrlDimensions                   = []string{DimensionPageUrl, DimensionDate}
	ChannelDimensions                   = []string{DimensionCustomChannelName, DimensionCustomChannelId, DimensionDate}
	ChannelCountryDimensions            = []string{DimensionCustomChannelName, DimensionCustomChannelId, DimensionDate, DimensionCountryName}
	UrlChannelDimensions                = []string{DimensionUrlChannelName, DimensionDate}
	UrlChannelCountryDimensions         = []string{DimensionUrlChannelName, DimensionDate, DimensionCountryName}
	SiteAdFormatDimensions              = []string{DimensionDomainCode, DimensionDate, DimensionAdFormatCode}
	SiteAdUnitDimensions                = []string{DimensionDomainCode, DimensionDate, DimensionAdUnitName}
	SiteCountryAdFormatDimensions       = []string{DimensionDomainCode, DimensionDate, DimensionAdFormatCode, DimensionCountryName}
	ChannelAdFormatDimensions           = []string{DimensionCustomChannelName, DimensionCustomChannelId, DimensionDate, DimensionAdFormatCode}
	ChannelCountryAdFormatDimensions    = []string{DimensionCustomChannelName, DimensionCustomChannelId, DimensionDate, DimensionAdFormatCode, DimensionCountryName}
	PageUrlAdFormatDimensions           = []string{DimensionPageUrl, DimensionDate, DimensionAdFormatCode}
	PageUrlCountryAdFormatDimensions    = []string{DimensionPageUrl, DimensionDate, DimensionAdFormatCode, DimensionCountryName}
	PageUrlCountryDimensions            = []string{DimensionPageUrl, DimensionDate, DimensionCountryName}
	UrlChannelAdFormatDimensions        = []string{DimensionUrlChannelName, DimensionDate, DimensionAdFormatCode}
	UrlChannelCountryAdFormatDimensions = []string{DimensionUrlChannelName, DimensionDate, DimensionAdFormatCode, DimensionCountryName}
)
