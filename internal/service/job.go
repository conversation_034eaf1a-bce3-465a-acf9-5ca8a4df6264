package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/sourcegraph/conc"
	"google.golang.org/protobuf/types/known/emptypb"

	pb "git.minigame.vip/minicloud/service/adsense-bot/api/job/v1"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/date"
)

// JobExecutionResult 任务执行结果统计
type JobExecutionResult struct {
	TaskName      string
	ExecutionMode string
	TotalAccounts int
	SuccessCount  int
	FailureCount  int
	Duration      time.Duration
}

var Jobs map[string]JobFunc

type JobFunc func(ctx context.Context, jobConfig *conf.Job_JobConfig) error
type MultiAccountJobFunc func(ctx context.Context, pubID, dateRange, currencyCode string) error

type JobService struct {
	uc               *biz.AdsenseUsecase
	shardingSchemaUc *biz.ShardingSchemaUsecase
	analyticsUseCase *biz.AnalyticsUsecase
	purchaseUsecase  *biz.PurchaseUsecase
	jobLogRepo       biz.JobLogRepo

	log *log.Helper

	pb.UnimplementedJobAdminServer
}

func NewJobService(uc *biz.AdsenseUsecase, analyticsUseCase *biz.AnalyticsUsecase, purchaseUsecase *biz.PurchaseUsecase,
	shardingSchemaUc *biz.ShardingSchemaUsecase, jobLogRepo biz.JobLogRepo, logger log.Logger) *JobService {
	job := &JobService{
		uc:               uc,
		shardingSchemaUc: shardingSchemaUc,
		purchaseUsecase:  purchaseUsecase,
		analyticsUseCase: analyticsUseCase,
		jobLogRepo:       jobLogRepo,
		log:              log.NewHelper(log.With(logger, "module", "service/job")),
	}
	return job
}

// logJobStart 记录任务开始日志
func (s *JobService) logJobStart(taskName string, accountCount int) {
	s.log.Infof("Task %s started with %d accounts", taskName, accountCount)
}

// logJobResult 记录任务执行结果汇总
func (s *JobService) logJobResult(result *JobExecutionResult) {
	if result.FailureCount == 0 {
		s.log.Infof("Task %s completed successfully: %d/%d accounts, duration: %dms",
			result.TaskName, result.SuccessCount, result.TotalAccounts, result.Duration.Milliseconds())
	} else if result.SuccessCount == 0 {
		s.log.Errorf("Task %s failed completely: 0/%d accounts succeeded, duration: %dms",
			result.TaskName, result.TotalAccounts, result.Duration.Milliseconds())
	} else {
		s.log.Warnf("Task %s completed with failures: %d/%d accounts succeeded, duration: %dms",
			result.TaskName, result.SuccessCount, result.TotalAccounts, result.Duration.Milliseconds())
	}
}

// logSimpleJobStart 记录简单任务开始日志
func (s *JobService) logSimpleJobStart(taskName string) {
	s.log.Infof("Simple task %s started", taskName)
}

// logSimpleJobResult 记录简单任务结果
func (s *JobService) logSimpleJobResult(taskName string, duration time.Duration, err error) {
	if err != nil {
		s.log.Errorf("Simple task %s failed after %dms: %v", taskName, duration.Milliseconds(), err)
	} else {
		s.log.Infof("Simple task %s completed successfully in %dms", taskName, duration.Milliseconds())
	}
}

// saveAccountJobLog 保存单个账户的任务执行日志
func (s *JobService) saveAccountJobLog(ctx context.Context, taskName, pubID, dateRange, currencyCode string, isSuccess bool, message string) {
	jobLog := &biz.JobLog{
		Date:         time.Now(),
		IsSuccess:    isSuccess,
		Message:      message,
		Name:         taskName,
		CurrencyCode: currencyCode,
		DateRange:    dateRange,
		Account:      pubID,
		Platform:     "Adsense",
	}

	err := s.jobLogRepo.Create(ctx, jobLog)
	if err != nil {
		s.log.Errorf("保存账户 %s 的任务日志失败: %v", pubID, err)
	}
}

// saveSimpleJobLog 保存简单任务的执行日志
func (s *JobService) saveSimpleJobLog(ctx context.Context, taskName, dateRange, currencyCode string, isSuccess bool, message string) {
	jobLog := &biz.JobLog{
		Date:         time.Now(),
		IsSuccess:    isSuccess,
		Message:      message,
		Name:         taskName,
		CurrencyCode: currencyCode,
		DateRange:    dateRange,
		Account:      "SYSTEM",
		Platform:     "System",
	}

	err := s.jobLogRepo.Create(ctx, jobLog)
	if err != nil {
		s.log.Errorf("保存任务 %s 的日志失败: %v", taskName, err)
	}
}

func (s *JobService) StartJob(_ context.Context, req *pb.StartJobRequest) (rsp *emptypb.Empty, err error) {
	wg := conc.NewWaitGroup()
	for _, name := range req.GetNames() {
		jobFunc, ok := Jobs[name]
		if !ok {
			s.log.Warnf("can not find job: %s", name)
			continue
		}
		wg.Go(func() {
			// 创建默认的job配置
			jobConfig := &conf.Job_JobConfig{
				Name:          name,
				DateRange:     "",
				CurrencyCode:  biz.USD,
				ExecutionMode: conf.AccountExecutionMode_ALL, // 默认为ALL模式
			}
			err := jobFunc(context.TODO(), jobConfig)
			if err != nil {
				panic(err)
			}
		})

	}
	err = wg.WaitAndRecover().AsError()
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *JobService) AutoMigrate(ctx context.Context, req *pb.AutoMigrateRequest) (rsp *emptypb.Empty, err error) {
	//for _, year := range req.GetYears() {
	err = s.shardingSchemaUc.AutoMigrate(ctx, int(req.GetYears()[0]))
	if err != nil {
		return
	}
	//}
	return &emptypb.Empty{}, nil
}

// createMultiAccountJobWrapper 创建多账号任务包装器
func (s *JobService) createMultiAccountJobWrapper(accountJob MultiAccountJobFunc) JobFunc {
	return func(ctx context.Context, jobConfig *conf.Job_JobConfig) error {
		startTime := time.Now()
		taskName := jobConfig.GetName()
		executionMode := jobConfig.GetExecutionMode()
		if executionMode == conf.AccountExecutionMode_UNSPECIFIED {
			executionMode = conf.AccountExecutionMode_ALL
		}

		// 根据配置获取需要执行的账号列表
		pubIDs, err := s.uc.GetPubIDsForJob(jobConfig)
		if err != nil {
			s.log.Errorw("failed_to_get_account_list",
				"task", taskName,
				"execution_mode", executionMode.String(),
				"error", err.Error(),
			)
			return fmt.Errorf("failed to get account list for task %s: %w", taskName, err)
		}

		if len(pubIDs) == 0 {
			s.log.Errorw("no_accounts_available",
				"task", taskName,
				"execution_mode", executionMode.String(),
			)
			return fmt.Errorf("no AdSense accounts available for task %s", taskName)
		}

		// 记录任务开始
		s.logJobStart(taskName, len(pubIDs))

		dateRange := jobConfig.GetDateRange()
		currencyCode := jobConfig.GetCurrencyCode()
		if currencyCode == "" {
			currencyCode = "USD"
		}

		var errors []error
		var failedAccounts []string
		var successfulAccounts []string

		// 处理所有账号
		for _, pubID := range pubIDs {
			err := accountJob(ctx, pubID, dateRange, currencyCode)
			if err != nil {
				errors = append(errors, err)
				failedAccounts = append(failedAccounts, pubID)
				// 为失败的账户记录日志
				s.saveAccountJobLog(ctx, taskName, pubID, dateRange, currencyCode, false, err.Error())
			} else {
				successfulAccounts = append(successfulAccounts, pubID)
				// 为成功的账户记录日志
				s.saveAccountJobLog(ctx, taskName, pubID, dateRange, currencyCode, true, "account processed successfully")
			}
		}

		// 创建执行结果
		result := &JobExecutionResult{
			TaskName:      taskName,
			ExecutionMode: executionMode.String(),
			TotalAccounts: len(pubIDs),
			SuccessCount:  len(successfulAccounts),
			FailureCount:  len(errors),
			Duration:      time.Since(startTime),
		}

		// 记录任务执行结果
		s.logJobResult(result)

		// 如果所有账号都失败，返回错误
		if len(errors) == len(pubIDs) {
			return fmt.Errorf("task %s failed for all %d accounts", taskName, len(pubIDs))
		}

		return nil
	}
}

// createSimpleJobWrapper 创建简单任务包装器
func (s *JobService) createSimpleJobWrapper(simpleJob func(ctx context.Context, dateRange, currencyCode string) error) JobFunc {
	return func(ctx context.Context, jobConfig *conf.Job_JobConfig) error {
		startTime := time.Now()
		taskName := jobConfig.GetName()
		dateRange := jobConfig.GetDateRange()
		currencyCode := jobConfig.GetCurrencyCode()
		if currencyCode == "" {
			currencyCode = "USD"
		}

		// 记录任务开始
		s.logSimpleJobStart(taskName)

		err := simpleJob(ctx, dateRange, currencyCode)
		duration := time.Since(startTime)

		// 记录任务结果
		s.logSimpleJobResult(taskName, duration, err)

		// 保存任务日志到数据库
		if err != nil {
			s.saveSimpleJobLog(ctx, taskName, dateRange, currencyCode, false, err.Error())
			return fmt.Errorf("task %s failed: %w", taskName, err)
		}

		s.saveSimpleJobLog(ctx, taskName, dateRange, currencyCode, true, "task completed successfully")
		return nil
	}
}

func (s *JobService) Init() {
	Jobs = map[string]JobFunc{
		JobNameSiteCountry:         s.createMultiAccountJobWrapper(s.siteCountryDataForAccount),
		JobNameSite:                s.createMultiAccountJobWrapper(s.siteDataForAccount),
		JobNameSiteAdFormat:        s.createMultiAccountJobWrapper(s.siteAdFormatDataForAccount),
		JobNameSiteAdUnit:          s.createMultiAccountJobWrapper(s.siteAdUnitDataForAccount),
		JobNameSiteAdFormatHistory: s.createMultiAccountJobWrapper(s.siteAdFormatHistoryDataForAccount),
		JobNameSiteCountryAdFormat: s.createMultiAccountJobWrapper(s.siteCountryAdFormatDataForAccount),

		JobNamePageUrl:                s.createMultiAccountJobWrapper(s.pageURLDataForAccount),
		JobNamePageUrlAdFormat:        s.createMultiAccountJobWrapper(s.pageURLAdFormatDataForAccount),
		JobNamePageUrlCountryAdFormat: s.createMultiAccountJobWrapper(s.pageURLCountryAdFormatDataForAccount),
		JobNamePageUrlCountry:         s.createMultiAccountJobWrapper(s.pageURLCountryDataForAccount),

		JobNameChannelCountry:         s.createMultiAccountJobWrapper(s.channelCountryDataForAccount),
		JobNameChannelAdFormat:        s.createMultiAccountJobWrapper(s.channelAdFormatDataForAccount),
		JobNameChannel:                s.createMultiAccountJobWrapper(s.channelDataForAccount),
		JobNameChannelCountryAdFormat: s.createMultiAccountJobWrapper(s.channelCountryAdFormatDataForAccount),

		JobNameUrlChannelCountry:         s.createMultiAccountJobWrapper(s.urlChannelCountryDataForAccount),
		JobNameUrlChannel:                s.createMultiAccountJobWrapper(s.urlChannelDataForAccount),
		JobNameUrlChannelAdFormat:        s.createMultiAccountJobWrapper(s.urlChannelAdFormatDataForAccount),
		JobNameUrlChannelCountryAdFormat: s.createMultiAccountJobWrapper(s.urlChannelCountryAdFormatDataForAccount),

		JobNameCustomChannel:                     s.createMultiAccountJobWrapper(s.customChannelDataForAccount),
		JobNameGenerateCooperationSiteUrlChannel: s.createMultiAccountJobWrapper(s.generateCooperationSiteUrlChannelForAccount),

		// 非多账号任务需要适配新的函数签名
		JobNameGenerateGames:              s.createSimpleJobWrapper(s.generateGamesTable),
		JobNameGenerateGamesHkd:           s.createSimpleJobWrapper(s.generateGamesHKDTable),
		JobNameGenerateGamesCountry:       s.createSimpleJobWrapper(s.generateGamesCountryTable),
		JobNameGenerateGamesCountryHkd:    s.createSimpleJobWrapper(s.generateGamesCountryHKDTable),
		JobNameAutoCreateSchema:           s.createSimpleJobWrapper(s.autoCreateSchema),
		JobNameAppCountrySummary:          s.createSimpleJobWrapper(s.appCountrySummary),
		JobNameDjsSiteAdFormatCountryData: s.createSimpleJobWrapper(s.djsSiteAdFormatCountryData),
		JobNameAnalyticsBase:              s.createSimpleJobWrapper(s.analyticsBaseData),
		JobNameAnalyticsSite:              s.createSimpleJobWrapper(s.analyticsSiteData),
		JobNameMergeSite:                  s.createSimpleJobWrapper(s.mergeSite),
		JobNameGenerateGaChannelReport:    s.createSimpleJobWrapper(s.generateGaChannelReport),
		JobNameTgAd:                       s.createSimpleJobWrapper(s.tgAdData),
		JobPurchaseInfo:                   s.createSimpleJobWrapper(s.purchaseInfoData),
		JobPurchaseSummaryInfo:            s.createSimpleJobWrapper(s.purchaseSummaryData),

		JobBusinessSite: s.createSimpleJobWrapper(s.businessSiteData),
	}
}

// 多账号版本的任务方法
func (s *JobService) siteCountryDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteCountryDimensions
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}

	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, pubID, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		return fmt.Errorf("failed to get AdSense data for account %s: %w", pubID, err)
	}

	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalDataForAccount(ctx, pubID, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			return fmt.Errorf("failed to get historical data for account %s: %w", pubID, err)
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}

	err = s.uc.SaveSiteCountryData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		return fmt.Errorf("failed to save site country data for account %s: %w", pubID, err)
	}
	return nil
}

// fetchAdditionalHistoricalDataForAccount 获取指定账号的额外历史数据（15天和30天）
func (s *JobService) fetchAdditionalHistoricalDataForAccount(ctx context.Context, pubID string, metrics, orderBy, dimensions []string, currencyCode string) ([][]string, error) {
	var allData [][]string

	// 定义需要获取的日期范围
	dateRanges := HistoricalDateRanges

	for _, dateRangeType := range dateRanges {
		generateDateRange, err := date.GenerateDateRange(dateRangeType)
		if err != nil {
			return nil, fmt.Errorf("failed to generate date range for %s: %w", dateRangeType, err)
		}

		// 只要前15当天的数据
		data, err := s.uc.GetAdSenseDataByCustomDateRange(ctx, pubID, metrics, orderBy, dimensions, []string{}, currencyCode, &date.Range{
			StartTime: generateDateRange.StartTime,
			EndTime:   generateDateRange.StartTime, // 只要当天的数据，不需要范围
		})
		if err != nil {
			return nil, fmt.Errorf("failed to get AdSense data for %s: %w", dateRangeType, err)
		}

		// 将数据追加到结果中
		allData = append(allData, data.Data...)
	}

	return allData, nil
}

func (s *JobService) pageURLDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := PageUrlDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}

	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		return fmt.Errorf("failed to get page URL data for account %s: %w", pubID, err)
	}

	err = s.uc.SavePageURLData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		return fmt.Errorf("failed to save page URL data for account %s: %w", pubID, err)
	}
	return nil
}

func (s *JobService) channelCountryDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := ChannelCountryDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}

	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		return fmt.Errorf("failed to get channel country data for account %s: %w", pubID, err)
	}

	err = s.uc.SaveChannelCountryData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		return fmt.Errorf("failed to save channel country data for account %s: %w", pubID, err)
	}
	return nil
}

func (s *JobService) urlChannelCountryDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := UrlChannelCountryDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelCountryData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveUrlChannelCountryData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelCountryData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) siteDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}

	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		return fmt.Errorf("failed to get site data for account %s: %w", pubID, err)
	}

	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalDataForAccount(ctx, pubID, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			return fmt.Errorf("failed to get historical data for account %s: %w", pubID, err)
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}

	err = s.uc.SaveSiteData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		return fmt.Errorf("failed to save site data for account %s: %w", pubID, err)
	}

	if dateRange == DateRangeToday {
		err := s.uc.SaveUnifiedSite(ctx, adSenseData)
		if err != nil {
			return fmt.Errorf("failed to save unified site data for account %s: %w", pubID, err)
		}
	}
	return nil
}

func (s *JobService) siteAdFormatDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := NewStandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteAdFormatDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdFormatData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalDataForAccount(ctx, pubID, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			return err
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}
	err = s.uc.SaveSiteAdFormatData(ctx, adSenseData, currencyCode, dateRange != DateRangeToday, dateRange, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdFormatData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) siteAdUnitDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := NewStandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteAdUnitDimensions
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}

	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, pubID, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdUnitData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalDataForAccount(ctx, pubID, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			s.log.WithContext(ctx).Errorf("[siteAdUnitData]: 账号 %s 获取历史数据失败: %v", pubID, err)
			return err
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}
	err = s.uc.SaveSiteAdUnitData(ctx, adSenseData, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdUnitData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) siteAdFormatHistoryDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := NewStandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteAdFormatDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdFormatHistoryData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveSiteAdFormatHistoryData(ctx, adSenseData)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteAdFormatHistoryData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) siteCountryAdFormatDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := SiteCountryAdFormatDimensions
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, pubID, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteCountryAdFormatData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	if dateRange != DateRangeToday {
		// 获取额外的历史数据（15天和30天）
		additionalData, err := s.fetchAdditionalHistoricalDataForAccount(ctx, pubID, metrics, orderBy, dimensions, currencyCode)
		if err != nil {
			return err
		}
		// 合并所有数据
		adSenseData.Data = append(adSenseData.Data, additionalData...)
	}
	err = s.uc.SaveSiteCountryAdFormatData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[siteCountryAdFormatData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) channelDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := ChannelDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveChannelData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) channelAdFormatDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := ChannelAdFormatDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelAdFormatData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveChannelAdFormatData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelAdFormatData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) pageURLAdFormatDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := PageUrlAdFormatDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLAdFormatData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SavePageURLAdFormatData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLAdFormatData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) urlChannelDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := UrlChannelDimensions
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveUrlChannelData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) urlChannelAdFormatDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := UrlChannelAdFormatDimensions
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelAdFormatData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveUrlChannelAdFormatData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelAdFormatData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) urlChannelCountryAdFormatDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := UrlChannelCountryAdFormatDimensions
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, pubID, metrics, orderBy, dimensions, []string{}, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelCountryAdFormatData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveUrlChannelCountryAdFormatData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[urlChannelCountryAdFormatData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) channelCountryAdFormatDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := ChannelCountryAdFormatDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseDataByCsv(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelCountryAdFormatData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveChannelCountryAdFormatData(ctx, adSenseData, currencyCode, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[channelCountryAdFormatData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) customChannelDataForAccount(ctx context.Context, pubID, _, _ string) error {
	customChannelData, err := s.uc.GetAdsenseCustomChannelData(ctx, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[customChannelData]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveCustomChannelData(ctx, customChannelData)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[customChannelData]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) generateCooperationSiteUrlChannelForAccount(ctx context.Context, pubID, _, _ string) error {
	urlChannelData, err := s.uc.GetAdsenseUrlChannelData(ctx, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateCooperationSiteUrlChannel]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	site, err := s.uc.GetCooperationSite(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateCooperationSiteUrlChannel]: 账号 %s 获取合作站点失败: %v", pubID, err)
		return err
	}
	err = s.uc.SaveNoCooperationSite(ctx)
	if err != nil {
		return err
	}
	err = s.uc.SaveCooperationSiteUrlChannel(ctx, urlChannelData, site)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateCooperationSiteUrlChannel]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.CreateOrUpdateCooperation(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[generateCooperationSiteUrlChannel]: 账号 %s 创建或更新合作失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) djsSiteAdFormatCountryData(ctx context.Context, _, _ string) error {
	metrics := DjsAdSenseMetrics
	dimensions := DjsAdSenseDimensions

	adSenseData, err := s.uc.GetDjsSiteAdFormatCountryData(ctx, metrics, dimensions)
	if err != nil {
		return fmt.Errorf("failed to get DJS site ad format country data: %w", err)
	}

	data, err := s.uc.GenerateExcel(append(dimensions, metrics...), adSenseData)
	if err != nil {
		return fmt.Errorf("failed to generate Excel report: %w", err)
	}

	err = s.uc.SendEmail(ctx, data, &biz.SendInfo{
		ReportName: ReportNameDjsTodayData,
		DateRange:  DateRangeToday,
		SendTime:   SendTime4Hour,
	})
	if err != nil {
		return fmt.Errorf("failed to send email report: %w", err)
	}
	return nil
}

func (s *JobService) generateGamesHKDTable(ctx context.Context, dateRange, _ string) error {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}

	start, end := date.GetDateRange(dateRange, time.Now().In(cstSh))

	pvs, err := s.uc.ListHKDChannels(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list HKD channel PV data: %w", err)
	}

	adFormatsMap, err := s.uc.ListHKDChannelAdFormats(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list HKD channel ad formats: %w", err)
	}

	err = s.uc.MergeHKDChannels(ctx, pvs, adFormatsMap)
	if err != nil {
		return fmt.Errorf("failed to merge HKD channels: %w", err)
	}

	return nil
}

func (s *JobService) generateGamesTable(ctx context.Context, dateRange, _ string) error {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}

	start, end := date.GetDateRange(dateRange, time.Now().In(cstSh))

	pvs, err := s.uc.ListChannels(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list channel PV data: %w", err)
	}

	adFormatsMap, err := s.uc.ListChannelAdFormats(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list channel ad formats: %w", err)
	}

	err = s.uc.MergeChannels(ctx, pvs, adFormatsMap)
	if err != nil {
		return fmt.Errorf("failed to merge channels: %w", err)
	}

	return nil
}
func (s *JobService) generateGamesCountryTable(ctx context.Context, dateRange, _ string) error {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	start, end := date.GetDateRange(dateRange, time.Now().In(cstSh))

	pvs, err := s.uc.ListChannelCountriesData(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list channel country pv data: %w", err)
	}
	adFormatsMap, err := s.uc.ListChannelCountryAdFormats(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list channel country ad format: %w", err)
	}
	err = s.uc.MergeChannelsCountry(ctx, pvs, adFormatsMap)
	if err != nil {
		return fmt.Errorf("failed to generate game country data: %w", err)
	}
	return nil
}
func (s *JobService) generateGamesCountryHKDTable(ctx context.Context, dateRange, _ string) error {
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	start, end := date.GetDateRange(dateRange, time.Now().In(cstSh))

	pvs, err := s.uc.ListChannelCountriesHKDData(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list channel country HKD pv data: %w", err)
	}
	adFormatsMap, err := s.uc.ListChannelCountryAdFormatsHKD(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list channel country HKD ad format: %w", err)
	}
	err = s.uc.MergeChannelsCountryHKD(ctx, pvs, adFormatsMap)
	if err != nil {
		return fmt.Errorf("failed to generate game country HKD data: %w", err)
	}
	return nil
}

func (s *JobService) autoCreateSchema(ctx context.Context, dateRange, _ string) error {
	year := time.Now().Year() + 1
	var err error
	if dateRange != "" {
		year, err = strconv.Atoi(dateRange)
		if err != nil {
			return fmt.Errorf("failed to parse year from dateRange %s: %w", dateRange, err)
		}
	}

	err = s.shardingSchemaUc.CreateTableSchema(ctx, year)
	if err != nil {
		return fmt.Errorf("failed to create table schema for year %d: %w", year, err)
	}
	return nil
}

func (s *JobService) appCountrySummary(ctx context.Context, _, _ string) error {
	err := s.uc.SaveAppCountrySummary(ctx)
	if err != nil {
		return fmt.Errorf("failed to save app country summary: %w", err)
	}
	return nil
}

func (s *JobService) Fetch(ctx context.Context, number uint32) error {
	err := s.uc.FetchEmailData(ctx, number)
	if err != nil {
		return err
	}
	return nil
}

func (s *JobService) analyticsBaseData(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	err := s.analyticsUseCase.SaveAnalyticsBaseData(ctx, dateRange)
	if err != nil {
		return fmt.Errorf("failed to get google analytics data: %w", err)
	}

	return nil
}

func (s *JobService) analyticsSiteData(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	err := s.analyticsUseCase.SaveAnalyticsSiteData(ctx, dateRange)
	if err != nil {
		return fmt.Errorf("failed to get google analytics site data: %w", err)
	}

	return nil
}

func (s *JobService) mergeSite(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	start, end := date.GetDateRange(dateRange, time.Now())

	siteMaps, err := s.uc.ListSites(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list adsense site data: %w", err)
	}
	adManagerSiteMap, err := s.uc.ListAdManagerSites(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to list ad manager site data: %w", err)
	}
	err = s.uc.MergeSites(ctx, siteMaps, adManagerSiteMap)
	if err != nil {
		return fmt.Errorf("failed to merge adsense adx site data: %w", err)
	}
	return nil
}

func (s *JobService) tgAdData(ctx context.Context, _, _ string) error {
	err := s.uc.SaveTgAd(ctx)
	if err != nil {
		return fmt.Errorf("failed to save tg ad data: %w", err)
	}
	return nil
}

func (s *JobService) generateGaChannelReport(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = "MONTH_TO_DATE" // 这个值在date包中定义，暂时保持硬编码
	}
	now := time.Now().Local()
	start, end := date.GetDateRange(dateRange, now)
	if dateRange == "MONTH_TO_DATE" {
		end = end.AddDate(0, 0, -1)
	}
	// 如果是1号，就查询上个月的数据
	if now.Day() == 1 {
		start, end = date.GetDateRange("LAST_MONTH", now)
	}

	estimatedEarningsData, err := s.uc.GetSiteEstimatedEarnings(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to get site estimated earnings: %w", err)
	}
	gaData, gaVisitPageCountMap, err := s.uc.GetSiteGaData(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to get site ga data: %w", err)
	}
	maxSiteEstimatedEarningsData, err := s.uc.GetMaxSiteEstimatedEarnings(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to get max site estimated earnings: %w", err)
	}
	data, err := s.uc.GenerateGaChannelReportExcel(estimatedEarningsData, gaData, gaVisitPageCountMap, maxSiteEstimatedEarningsData)
	if err != nil {
		return fmt.Errorf("failed to generate ga channel report: %w", err)
	}
	err = s.uc.SendGaChannelReportEmail(ctx, data, &biz.SendInfo{
		ReportName: fmt.Sprintf("渠道监控报表 %s-%s", start.Format(time.DateOnly), end.Format(time.DateOnly)),
	})
	if err != nil {
		return fmt.Errorf("failed to send ga channel report: %w", err)
	}
	return nil
}

func (s *JobService) purchaseInfoData(ctx context.Context, _, _ string) error {
	// 调用业务逻辑层处理
	err := s.purchaseUsecase.FetchAndCreatePurchases(ctx)
	if err != nil {
		return fmt.Errorf("从外部API获取并创建内购数据失败: %w", err)
	}

	return nil
}

func (s *JobService) purchaseSummaryData(ctx context.Context, _, _ string) error {
	// 调用业务逻辑层处理汇总数据
	err := s.purchaseUsecase.FetchAndCreatePurchaseSummaries(ctx)
	if err != nil {
		return fmt.Errorf("从外部API获取并创建内购汇总数据失败: %w", err)
	}

	return nil
}

func (s *JobService) pageURLCountryAdFormatDataForAccount(ctx context.Context, pubID, dateRange, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := PageUrlCountryAdFormatDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLCountryAdFormatDataForAccount]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SavePageURLCountryAdFormatData(ctx, adSenseData, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLCountryAdFormatDataForAccount]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

func (s *JobService) pageURLCountryDataForAccount(ctx context.Context, pubID string, dateRange string, currencyCode string) error {
	metrics := StandardAdSenseMetrics
	orderBy := StandardOrderBy
	dimensions := PageUrlCountryDimensions
	var filters []string
	if dateRange == "" {
		dateRange = DateRangeYesterday
	}
	if currencyCode == "" {
		currencyCode = biz.USD
	}
	adSenseData, err := s.uc.GetAdSenseData(ctx, pubID, metrics, orderBy, dimensions, filters, dateRange, currencyCode)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLCountryDataForAccount]: 账号 %s 获取数据失败: %v", pubID, err)
		return err
	}
	err = s.uc.SavePageURLCountryData(ctx, adSenseData, pubID)
	if err != nil {
		s.log.WithContext(ctx).Errorf("[pageURLCountryDataForAccount]: 账号 %s 保存数据失败: %v", pubID, err)
		return err
	}
	return nil
}

// businessSiteData 生成商户平台渠道特定的数据
func (s *JobService) businessSiteData(ctx context.Context, dateRange, _ string) error {
	if dateRange == "" {
		dateRange = DateRangeLast7Days
	}
	start, end := date.GetDateRange(dateRange, time.Now())

	err := s.uc.SaveBusinessSiteData(ctx, start, end)
	if err != nil {
		return fmt.Errorf("failed to save business site data: %w", err)
	}

	return nil
}

