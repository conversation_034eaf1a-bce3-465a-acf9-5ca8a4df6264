syntax = "proto3";
package kratos.api;

option go_package = "adsense-bot/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  Job job = 3;
  google google = 4;
  Bot bot = 5;
  Adsense adsense = 6;
  Email email = 7;
  Log log = 8;
  Djs djs = 9;
  Auth auth = 10;
  Audit audit = 11;
  Imap imap = 12;
  Analytics analytics = 13;
  TgAd tg_ad = 14;
  ChannelGaReport channel_ga_report = 15;
  ExternalPurchaseAPI external_purchase_api = 16;
  OpenAdmin open_admin = 17;
}

message Adsense {
  int32 start_year = 1;
  int32 start_month = 2 ;
  int32  start_day = 3;
  int32 end_month = 4;
  int32  end_day = 5;
  int32  end_year = 6;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    .google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
}

message Bot {
  string key = 1;
}

message Email {
  string host = 1;
  int32 port = 2;
  string username = 3;
  string password = 4;
  repeated string nv_to = 5;
  repeated string nv_cc = 6;
}

message Djs {
  repeated string to = 5;
  repeated string site = 6;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    .google.protobuf.Duration read_timeout = 3;
    .google.protobuf.Duration write_timeout = 4;
  }
  // 分表
  message Sharding {
    // 临时表有效间隔时间
    int64  interval = 1;
    // 分区保留月份,（Eg:6 表示:保留6个月）
    int64  out_of_date_month = 2;
  }

  Database database = 1;
  Redis redis = 2;
  Sharding sharding = 3;
}

message Job {
  message JobConfig {
    string name = 1;
    string schedule = 2;
    string input = 3;
    string output = 4;
    string date_range = 5;
    string currency_code = 6;
    // 账号相关配置
    string pub_id = 7;                    // 指定单个账号的pubID
    repeated string pub_ids = 8;          // 指定多个账号的pubID列表
    bool all_accounts = 9;                // 是否对所有启用的账号执行
    AccountExecutionMode execution_mode = 10; // 执行模式
  }
  repeated JobConfig jobs = 1;
}

// 账号执行模式
enum AccountExecutionMode {
  UNSPECIFIED = 0; // 未指定模式（默认为ALL模式）
  SINGLE = 1;      // 单账号模式（使用pub_id字段）
  MULTIPLE = 2;    // 多账号模式（使用pub_ids字段）
  ALL = 3;         // 所有账号模式（忽略pub_id和pub_ids字段）
}



message google {
  repeated GoogleConfig configs = 1;
}

message GoogleConfig {
  string account_id = 1;        // 账号唯一标识（可以使用pubID或自定义ID）
  string account_name = 2;      // 账号名称（便于管理和识别）
  string clientID = 3;
  string clientSecret = 4;
  string code = 5;              // refresh token
  string pubID = 6;
  bool use_sa = 7;              // 是否使用服务账号
  bool enabled = 8;             // 是否启用该账号
  string description = 9;       // 账号描述
  repeated string tags = 10;    // 账号标签（用于分组管理）
}

message Log {
  // 日志存储路径
  string path = 1;
  // 日志级别
  string level = 2;
  // 日志文件大小
  int32 max_size = 3;
  // 日志文件最大存储时间
  int32 max_age = 4;
  // 日志文件最大存储数量
  int32 max_backups = 5;
  // 日志文件备份是否压缩
  bool compress = 6;
  // 是否输出到控制台
  bool console = 7;
}

message Auth {
  string api_key = 1;
}

message Audit {
  string addr = 1;
  bool tls = 2;
}

message Imap {
  string address = 1;
  string username = 2;
  string password = 3;
  bool   is_open = 4;
}


message Analytics {
  repeated string properties_id = 1;
  repeated string account_id = 2;
}

message TgAd {
  string url = 1;
}

message ChannelGaReport {
  repeated  string to = 1;
  double min_estimated_earnings = 2;
  double redu = 3;
}

// 外部购买数据API配置
message ExternalPurchaseAPI {
  string url = 1;
  .google.protobuf.Duration timeout = 2;
}

// OpenAdmin服务配置
message OpenAdmin {
  string addr = 1;
  // 是否启用TLS
  bool grpc_tls = 2;
  // 连接超时时间
  .google.protobuf.Duration timeout = 3;
  // 是否启用mock模式
  bool mock_mode = 4;
}