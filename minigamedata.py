#coding=utf-8
import subprocess
import time
import os
import json

config_file_template = "/datax/job/minigamedata.json"
config_file = "/datax/tmp/minigamedata.json"
datax_py = "/datax/bin/datax.py"


def update_config_runx():
    # conn = psycopg2.connect(database=db_config["database"], user=db_config["user"], password=db_config["password"], host="127.0.0.1", port="5432")
#     conn = psycopg2.connect(**db_config)
#     cursor = conn.cursor()

    with open(config_file_template+'.template', 'r') as f:
        config = json.load(f)
        f.close()

    # 读取和更新配置中的preValue
#     for table in table_configs:
#         last_incr = read_last_value(cursor, table["tableName"], table["columnName"])
#         replace_config(config, '#{'+table["preValue"]+'}', str(last_incr))

    # 单个json只能同步一个表结构，一对多，多对一均可；
    ic = 0
    contents = config['job']['content']
    for content in contents:
        ncontent = []
        ncontent.append(content)
        config['job']['content'] = ncontent
        new_config_file = config_file+str(ic)+'.json'
        with open(new_config_file, 'w') as f:
            json.dump(config, f, indent=4)
            f.close()
        ic=ic+1
        run_dataX(new_config_file)

#     cursor.close()
#     conn.close()

def run_dataX(config_file):
    print(subprocess.call(["python3 " + datax_py + ' ' + config_file], shell=True))

i = 0
while True:
    update_config_runx()
    print('### loop ',i)
    i=i+1
    time.sleep(1200)