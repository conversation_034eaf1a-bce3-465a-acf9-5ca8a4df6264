package date

import (
	"fmt"
	"time"
)

// Range 表示日期范围的结构体
type Range struct {
	StartTime time.Time
	EndTime   time.Time
}


// getNowInBeijing 获取北京时间（UTC+8）
func getNowInBeijing() time.Time {
	beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
	return time.Now().In(beijingLocation)
}

func Yesterday(now time.Time) (time.Time, time.Time) {
	date := now.AddDate(0, 0, -1)
	return time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location()),
		time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 59, date.Location())
}

// Last7Days 过去7天
func Last7Days(now time.Time) (time.Time, time.Time) {
	date := now.AddDate(0, 0, -1)

	return time.Date(date.Year(), date.Month(), date.Day()-6, 0, 0, 0, 0, date.Location()),
		time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 59, date.Location())
}

// Last30Days 过去30天
func Last30Days(now time.Time) (time.Time, time.Time) {
	date := now.AddDate(0, 0, -1)

	return time.Date(date.Year(), date.Month(), date.Day()-29, 0, 0, 0, 0, date.Location()), time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 59, date.Location())
}

// Last15Days 过去15天
func Last15Days(now time.Time) (time.Time, time.Time) {
	date := now.AddDate(0, 0, -1)

	return time.Date(date.Year(), date.Month(), date.Day()-14, 0, 0, 0, 0, date.Location()),
		time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 59, date.Location())
}

// ThisYear 今年
func ThisYear(now time.Time) (time.Time, time.Time) {
	//now := time.Now()
	date := now.AddDate(0, 0, -1)

	return time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location()), time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 59, date.Location())
}

// MonthToDate 这个月
func MonthToDate(now time.Time) (time.Time, time.Time) {
	//now := time.Now()

	return time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location()),
		time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
}

// LastMonth 上个月
func LastMonth(now time.Time) (time.Time, time.Time) {
	//now := time.Now()
	firstDayOfThisMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	firstDayOfLastMonth := firstDayOfThisMonth.AddDate(0, -1, 0)
	lastDayOfLastMonth := firstDayOfThisMonth.Add(-time.Second)
	return firstDayOfLastMonth, lastDayOfLastMonth
}

// YearToDate 过去3年
func YearToDate(nowTime time.Time) (time.Time, time.Time) {
	//now := time.Now()
	date := nowTime.AddDate(0, 0, -1)

	threeYearsAgo := nowTime.AddDate(-3, 0, 0)
	return threeYearsAgo, time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 59, date.Location())
}

func GetDateRange(dateRange string, nowTime time.Time) (time.Time, time.Time) {
	switch dateRange {
	case "YESTERDAY":
		return Yesterday(nowTime)
	case "LAST_7_DAYS":
		return Last7Days(nowTime)
	case "LAST_30_DAYS":
		return Last30Days(nowTime)
	case "MONTH_TO_DATE":
		return MonthToDate(nowTime)
	case "LAST_MONTH":
		return LastMonth(nowTime)
	case "YEAR_TO_DATE":
		return YearToDate(nowTime)
	case "THIS_YEAR":
		return ThisYear(nowTime)
	case "LAST_15_DAYS":
		return Last15Days(nowTime)
	case "TODAY":
		return nowTime, nowTime

	}
	return time.Now(), time.Now()
}

// TimeAdjustmentResult 时间调整结果
type TimeAdjustmentResult struct {
	AdjustedStartTime time.Time
	AdjustedEndTime   time.Time
	NeedsFiltering    bool // 如果开始时间和结束时间为同一天，则为false
}

// AdjustTimeToPreviousDay 将时间调整到前一天的16:00:00 UTC
// 如果开始时间和结束时间为同一天，则不需要过滤
// 例如：2025-07-22 10:05:39 +0000 UTC -> 2025-07-21 16:00:00 +0000 UTC
func AdjustTimeToPreviousDay(startTime, endTime time.Time) *TimeAdjustmentResult {
	// 将时间转换为UTC时区以确保一致性
	startUTC := startTime.UTC()
	endUTC := endTime.UTC()

	// 检查开始时间和结束时间是否为同一天
	startDate := time.Date(startUTC.Year(), startUTC.Month(), startUTC.Day(), 0, 0, 0, 0, time.UTC)
	endDate := time.Date(endUTC.Year(), endUTC.Month(), endUTC.Day(), 0, 0, 0, 0, time.UTC)

	// 如果是同一天，不需要过滤
	if startDate.Equal(endDate) {
		return &TimeAdjustmentResult{
			AdjustedStartTime: startUTC,
			AdjustedEndTime:   endUTC,
			NeedsFiltering:    false,
		}
	}

	// 将开始时间调整到前一天的16:00:00 UTC
	adjustedStart := time.Date(
		startUTC.Year(),
		startUTC.Month(),
		startUTC.Day()-1, // 前一天
		16, 0, 0, 0,      // 16:00:00
		time.UTC,
	)

	// 将结束时间调整到前一天的16:00:00 UTC
	adjustedEnd := time.Date(
		endUTC.Year(),
		endUTC.Month(),
		endUTC.Day()-1, // 前一天
		16, 0, 0, 0,    // 16:00:00
		time.UTC,
	)

	return &TimeAdjustmentResult{
		AdjustedStartTime: adjustedStart,
		AdjustedEndTime:   adjustedEnd,
		NeedsFiltering:    true,
	}
}

func GenerateDateRange(dateRange string) (*Range, error) {

	now := getNowInBeijing()

	switch dateRange {
	case "LAST_7_DAYS":
		// 过去7天：从8天前的00:00:00到昨天的23:59:59（不包括今天）
		startTime := now.AddDate(0, 0, -8).Truncate(24 * time.Hour)
		endTime := now.AddDate(0, 0, -1).Truncate(24 * time.Hour).Add(24*time.Hour - time.Nanosecond)
		return &Range{
			StartTime: startTime,
			EndTime:   endTime,
		}, nil

	case "LAST_30_DAYS":
		// 过去30天：从31天前的00:00:00到昨天的23:59:59（不包括今天）
		startTime := now.AddDate(0, 0, -31).Truncate(24 * time.Hour)
		endTime := now.AddDate(0, 0, -1).Truncate(24 * time.Hour).Add(24*time.Hour - time.Nanosecond)
		return &Range{
			StartTime: startTime,
			EndTime:   endTime,
		}, nil
	case "LAST_15_DAYS":
		// 过去15天：从16天前的00:00:00到昨天的23:59:59（不包括今天）
		startTime := now.AddDate(0, 0, -16).Truncate(24 * time.Hour)
		endTime := now.AddDate(0, 0, -1).Truncate(24 * time.Hour).Add(24*time.Hour - time.Nanosecond)
		return &Range{
			StartTime: startTime,
			EndTime:   endTime,
		}, nil
	case "YESTERDAY":
		// 昨天：从昨天的00:00:00到昨天的23:59:59
		yesterday := now.AddDate(0, 0, -1)
		startTime := yesterday.Truncate(24 * time.Hour)
		endTime := startTime.Add(24*time.Hour - time.Nanosecond)
		return &Range{
			StartTime: startTime,
			EndTime:   endTime,
		}, nil

	default:
		return nil, fmt.Errorf("unsupported date range: %s", dateRange)
	}
}
