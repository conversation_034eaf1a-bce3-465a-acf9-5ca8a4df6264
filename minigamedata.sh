#!/bin/sh

APP_NAME="minigamedata.py"
LOG_FILE="minigamedata.log"
PID_FILE="minigamedata.pid"

start_app() {
    # 获取脚本所在目录并切换到该目录
    SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
    cd "$SCRIPT_DIR"

    if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "[$(date)] $APP_NAME is already running (PID: $(cat $PID_FILE))"
    else
        echo "[$(date)] Starting $APP_NAME in directory: $SCRIPT_DIR..." | tee -a "$LOG_FILE"
        nohup python3 "$APP_NAME" >> "$LOG_FILE" 2>&1 &
        echo $! > "$PID_FILE"
        echo "[$(date)] Started $APP_NAME with PID: $(cat $PID_FILE)" | tee -a "$LOG_FILE"
    fi
}

stop_app() {
    # 获取脚本所在目录并切换到该目录
    SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
    cd "$SCRIPT_DIR"

    if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "[$(date)] Stopping $APP_NAME (PID: $(cat $PID_FILE))..." | tee -a "$LOG_FILE"
        kill $(cat "$PID_FILE")
        rm -f "$PID_FILE"
        echo "[$(date)] Stopped $APP_NAME" | tee -a "$LOG_FILE"
    else
        echo "[$(date)] $APP_NAME is not running" | tee -a "$LOG_FILE"
    fi
}

status_app() {
    # 获取脚本所在目录并切换到该目录
    SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
    cd "$SCRIPT_DIR"

    if [ -f "$PID_FILE" ] && kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "[$(date)] $APP_NAME is running (PID: $(cat $PID_FILE))"
    else
        echo "[$(date)] $APP_NAME is not running"
    fi
}

restart_app() {
    stop_app
    sleep 2
    start_app
}

case "$1" in
    start)
        start_app
        ;;
    stop)
        stop_app
        ;;
    restart)
        restart_app
        ;;
    status)
        status_app
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac