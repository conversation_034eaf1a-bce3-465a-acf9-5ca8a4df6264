package main

import (
	"flag"
	"os"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
	_ "go.uber.org/automaxprocs"

	"git.minigame.vip/minicloud/service/adsense-bot/extern/miniutils/log/zerolog"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/server"
	_ "git.minigame.vip/minicloud/service/adsense-bot/pkg/i18n"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = "adsense-bot"
	// Version is the version of the compiled software.
	Version = "1.51"
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
}

func newApp(logger log.Logger, worker *server.Worker, http *http.Server, imap *server.IMAPClient) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			worker,
			http,
			imap,
		),
	)
}

func main() {
	flag.Parse()
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
		),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	logger := log.With(zerolog.NewLogger(
		Name,
		zerolog.WithPath(bc.Log.Path),
		zerolog.WithCompress(bc.Log.Compress),
		zerolog.WithConsole(bc.Log.Console),
	),
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"caller", log.DefaultCaller,
		"ts", log.DefaultTimestamp,
	)

	app, cleanup, err := wireApp(bc.Server, bc.Data, bc.Job, bc.Google, bc.Bot, bc.Adsense, bc.Email, bc.Djs, logger, bc.Audit, bc.Auth, bc.Imap, bc.Analytics, bc.TgAd, bc.ChannelGaReport, bc.ExternalPurchaseApi, bc.OpenAdmin)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
