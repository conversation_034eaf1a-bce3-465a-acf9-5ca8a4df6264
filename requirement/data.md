数据中台
- [P0]多合作商广告账号收入数据同步（包含多个KA账号、小米、微软、flat等）， 处理并发布为商户平台的数据报告；
- [P0]游戏用户行为GA数据同步，基于网页网址清洗游戏渠道收入数据，发布为商户平台数据报告；
- [P2]投放端计划消耗数据同步，数据归因与BI数据面板建设；
- [P1]数仓建设、数据分层。


多合作商广告账号收入数据同步（包含多个KA账号、小米、微软、flat等）， 处理并发布为商户平台的数据报告；
1. 多个AdSense账号的数据采集支持，并为后续其它广告平台预留设计
2. 数据中台API修改
3. AdSense 账号的服务账号或Google账号的授权

游戏用户行为GA数据同步，基于网页网址清洗游戏渠道收入数据，发布为商户平台数据报告；
1. 新增 game 表，采用网页网址的数据计算
2. 新增 game_site 采用网页网址计算获得网站的游戏的数据
3. 新增 game_country 表，需要获取page_url_adformat_country数据，主要是AdSense api只支持30天的数据
4. 商户平台对应接口实现
5. 在现有的ga网站游戏数据上继续完善补充数据
6. 持续补充当前获取网页网站游戏id的用例